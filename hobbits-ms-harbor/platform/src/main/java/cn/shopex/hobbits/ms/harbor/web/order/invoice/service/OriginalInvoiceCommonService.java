package cn.shopex.hobbits.ms.harbor.web.order.invoice.service;

import cn.shopex.hobbits.ms.harbor.common.domain.order.ConversionOriginalInvoiceResultDto;
import cn.shopex.hobbits.ms.harbor.common.domain.order.InvoiceApplyDto;
import cn.shopex.hobbits.ms.harbor.configuration.ApplicationMqProperties;
import cn.shopex.hobbits.ms.orm.model.OriginalInvoice;
import cn.shopex.hobbits.ms.orm.model.OriginalInvoiceHistory;
import cn.shopex.hobbits.ms.orm.service.OriginalInvoiceHistoryService;

import java.util.Map;

/**
 * <AUTHOR>
 */
public interface OriginalInvoiceCommonService {

    /**
     * 保存开票申请数据信息
     *
     * @param originalInvoiceHistory 开票履历
     */
    void saveOriginalInvoice(OriginalInvoiceHistory originalInvoiceHistory);

    /**
     * 修改原始开票申请转换结果
     *
     * @param conversionOriginalInvoiceResultDto 转换结果
     */
    void updateOriginalInvoiceConversionResult(ConversionOriginalInvoiceResultDto conversionOriginalInvoiceResultDto);

    /**
     * 赋值原始开票申请
     *
     * @param originalInvoice 原始开票申请实体
     * @param invoiceApplyDto 原始开票申请信息
     * @param invoiceDetails  原始开票申请JSON
     * @param operateTime     操作时间
     * @param unixTime        操作时间戳
     * @return 原始开票申请
     */
    OriginalInvoice fillOriginalInvoice(OriginalInvoice originalInvoice, InvoiceApplyDto invoiceApplyDto, String invoiceDetails, String operateTime, Long unixTime);

    /**
     * 统一发送 发票申请信息至财务中心
     * @param invoiceApplyDto
     */
    void sendOriginalInvoice2FinanceMq(InvoiceApplyDto invoiceApplyDto);

}
