package cn.shopex.hobbits.ms.harbor.web.order.decryption.service;

import cn.shopex.hobbits.ms.orm.model.*;
import com.fasterxml.jackson.core.JsonProcessingException;

import java.util.Map;

/**
 * 订单解密service
 *
 * <AUTHOR>
 */
public interface OrderDecryptionService {

    /**
     * 保存订单解密信息
     *
     * @param onlineStoreType 店铺类型
     * @param receiptNumber   单据编号
     * @param receiptType     单据类型
     * @param orderInfo       订单信息map
     * @param operator        操作人
     * @param operatorTime    操作时间
     * @param operatorTimeUxt 操作时间戳
     * @throws JsonProcessingException json转换一场
     */
    void saveOrderDecryptionInfo(Integer onlineStoreType, String receiptNumber, Integer receiptType, Map<String, Object> orderInfo, String operator, String operatorTime, Long operatorTimeUxt) throws JsonProcessingException;

    /**
     * 根据单据编号和单据类型查询淘宝订单解密信息
     *
     * @param receiptNumber 单据编号
     * @param receiptType   单据类型
     * @return 淘宝订单解密信息
     */
    OrderDecryptionTaobao selectOrderDecryptionTaobaoByReceiptNumberAndReceiptType(String receiptNumber, Integer receiptType);

    /**
     * 根据单据编号和单据类型查询京东订单解密信息
     *
     * @param receiptNumber 单据编号
     * @param receiptType   单据类型
     * @return 京东订单解密信息
     */
    OrderDecryptionJD selectOrderDecryptionJDByReceiptNumberAndReceiptType(String receiptNumber, Integer receiptType);

    /**
     * 根据单据编号和单据类型查询抖音订单解密信息
     *
     * @param receiptNumber 单据编号
     * @param receiptType   单据类型
     * @return 抖音订单解密信息
     */
    OrderDecryptionDouyin selectOrderDecryptionDouyinByReceiptNumberAndReceiptType(String receiptNumber, Integer receiptType);

    /**
     * 根据单据编号和单据类型查询拼多多订单解密信息
     *
     * @param receiptNumber 单据编号
     * @param receiptType   单据类型
     * @return 拼多多订单解密信息
     */
    OrderDecryptionPinduoduo selectOrderDecryptionPinduoduoByReceiptNumberAndReceiptType(String receiptNumber, Integer receiptType);

    /**
     * 根据单据编号和单据类型查询小红书订单解密信息
     *
     * @param receiptNumber 单据编号
     * @param receiptType   单据类型
     * @return 小红书订单解密信息
     */
    public OrderDecryptionXiaohongshu selectOrderDecryptionXiaohongshuByReceiptNumberAndReceiptType(
            String receiptNumber, Integer receiptType);

    /**
     * 根据单据编号和单据类型查询唯品会订单解密信息
     *
     * @param receiptNumber 单据编号
     * @param receiptType   单据类型
     * @return 唯品会订单解密信息
     */
    public OrderDecryptionWeipinhui selectOrderDecryptionWeipinhuiByReceiptNumberAndReceiptType(
            String receiptNumber, Integer receiptType);

    /**
     * 根据单据编号和单据类型查询快手订单解密信息
     *
     * @param receiptNumber 单据编号
     * @param receiptType   单据类型
     * @return 快手订单解密信息
     */
    OrderDecryptionKs selectOrderDecryptionKsByReceiptNumberAndReceiptType(String receiptNumber, Integer receiptType);

    /**
     * 根据单据编号和单据类型查询私域订单解密信息
     *
     * @param receiptNumber 单据编号
     * @param receiptType   单据类型
     * @return 拼多多订单解密信息
     */
    OrderDecryptionPrivate selectOrderDecryptionPrivateByReceiptNumberAndReceiptType(String receiptNumber, Integer receiptType);

    /**
     * 根据单据编号和单据类型查询得物订单解密信息
     *
     * @param receiptNumber 单据编号
     * @param receiptType   单据类型
     * @return 得物订单解密信息
     */
    OrderDecryptionDewu selectOrderDecryptionDewuByReceiptNumberAndReceiptType(String receiptNumber, Integer receiptType);

    /**
     * 根据单据编号和单据类型查询MOMO订单解密信息
     *
     * @param receiptNumber 单据编号
     * @param receiptType   单据类型
     * @return 拼多多订单解密信息
     */
    OrderDecryptionMomo selectOrderDecryptionMomoByReceiptNumberAndReceiptType(String receiptNumber, Integer receiptType);

    OrderDecryptionJD selectOrderDecryptionJdByReceiptNumberAndReceiptType(
            String receiptNumber, Integer receiptType);

    /**
     * 根据单据编号和单据类型查询微信小店订单解密信息
     *
     * @param receiptNumber
     * @param receiptType
     * @return
     */
    OrderDecryptionWechatStore selectOrderDecryptionWechatStoreByReceiptNumberAndReceiptType(String receiptNumber, Integer receiptType);
}
