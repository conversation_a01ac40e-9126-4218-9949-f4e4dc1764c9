package cn.shopex.hobbits.ms.harbor.web.order.invoice.biz;

import cn.shopex.hobbits.ms.harbor.api.inner.biz.InvoicingBiz;
import cn.shopex.hobbits.ms.harbor.api.inner.domain.MatrixResult;
import cn.shopex.hobbits.ms.harbor.callback.order.domain.AlibabaEinvoiceApplyDto;
import cn.shopex.hobbits.ms.harbor.callback.order.domain.DouYinInvoiceApplyDto;
import cn.shopex.hobbits.ms.harbor.callback.order.handler.AlibabaOriginalInvoiceTransformer;
import cn.shopex.hobbits.ms.harbor.callback.order.handler.DouYinOriginalInvoiceTransformer;
import cn.shopex.hobbits.ms.harbor.callback.order.handler.OriginalInvoiceTransformer;
import cn.shopex.hobbits.ms.harbor.common.CommonConst;
import cn.shopex.hobbits.ms.harbor.common.MessageConst;
import cn.shopex.hobbits.ms.harbor.common.MessageQueueConst;
import cn.shopex.hobbits.ms.harbor.common.biz.AsyncBiz;
import cn.shopex.hobbits.ms.harbor.common.domain.RedundantChannel;
import cn.shopex.hobbits.ms.harbor.common.domain.RetailOrderDto;
import cn.shopex.hobbits.ms.harbor.common.domain.order.InvoiceApplyDto;
import cn.shopex.hobbits.ms.harbor.common.log.RecordContext;
import cn.shopex.hobbits.ms.harbor.common.log.RecordContextHolder;
import cn.shopex.hobbits.ms.harbor.common.order.constant.InvoiceApplyFieldConst;
import cn.shopex.hobbits.ms.harbor.common.order.conversion.OriginalInvoiceConversion;
import cn.shopex.hobbits.ms.harbor.common.service.OtherCenterDataService;
import cn.shopex.hobbits.ms.harbor.configuration.ApplicationMqProperties;
import cn.shopex.hobbits.ms.harbor.util.SpringContextUtil;
import cn.shopex.hobbits.ms.harbor.web.order.invoice.domain.*;
import cn.shopex.hobbits.ms.harbor.web.order.invoice.enums.DouYinInvoiceStatusEnum;
import cn.shopex.hobbits.ms.harbor.web.order.invoice.service.OriginalInvoiceBizService;
import cn.shopex.hobbits.ms.harbor.web.order.invoice.service.OriginalInvoiceCommonService;
import cn.shopex.hobbits.ms.library.domain.BatchOperationGenericRes;
import cn.shopex.hobbits.ms.library.domain.ErrorDataTitle;
import cn.shopex.hobbits.ms.library.domain.PageData;
import cn.shopex.hobbits.ms.library.exception.BizDataNotFoundException;
import cn.shopex.hobbits.ms.library.exception.BizServiceException;
import cn.shopex.hobbits.ms.orm.model.OriginalInvoice;
import cn.shopex.hobbits.ms.orm.model.OriginalInvoiceHistory;
import cn.shopex.hobbits.ms.orm.service.OriginalInvoiceHistoryService;
import cn.shopex.hobbits.ms.rmp.biz.producer.RMQCommProducer;
import cn.shopex.hobbits.ms.rmp.biz.producer.domain.RMQProducerBody;
import cn.shopex.hobbits.neb.domain.*;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static cn.shopex.hobbits.ms.harbor.common.CommonConst.CHANNEL_TYPE_ONLINE;
import static cn.shopex.hobbits.ms.library.utils.DateUtil.*;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class OriginalInvoiceBiz {
    private final OriginalInvoiceBizService originalInvoiceBizService;
    private final RMQCommProducer rmqCommProducer;
    private final ObjectMapper objectMapper;
    private final ApplicationMqProperties applicationMqProperties;
    private final OtherCenterDataService otherCenterDataService;
    private final InvoicingBiz invoicingBiz;
    private final AsyncBiz asyncBiz;
    private final OriginalInvoiceCommonService originalInvoiceCommonService;

    /**
     * 根据复杂条件查询符合条件的原始开票申请信息
     *
     * @param transactionNumber 平台原始交易订单号
     * @param sourcePlatforms   来源平台
     * @param conversionStatus  转换状态
     * @param page              页码
     * @param size              单页数量
     * @return 单页数据
     */
    public PageData<OriginalInvoice> selectOriginalInvoiceByCondition(String transactionNumber, String sourcePlatforms, String conversionStatus, Integer page, Integer size) {
        Map<String, Object> condition = new HashMap<>(5);
        condition.put("offset", page * size);
        condition.put("size", size);
        condition.put("transactionNumber", transactionNumber);

        if (StringUtils.isNotEmpty(sourcePlatforms)) {
            List<String> sourcePlatformList = Splitter.on(',').trimResults().omitEmptyStrings().splitToList(sourcePlatforms);
            condition.put("sourcePlatformList", sourcePlatformList);
        }
        if (StringUtils.isNotEmpty(conversionStatus)) {
            List<Integer> conversionStatusList = Splitter.on(',').trimResults().omitEmptyStrings().splitToList(conversionStatus).stream().map(Integer::parseInt).collect(Collectors.toList());
            condition.put("conversionStatusList", conversionStatusList);
        }

        List<OriginalInvoice> data = originalInvoiceBizService.selectOriginalInvoiceByCondition(condition);
        Long count = originalInvoiceBizService.selectCountOriginalInvoiceByCondition(condition);

        return PageData.<OriginalInvoice>builder().count(count).result(data).build();
    }

    /**
     * 根据平台原始交易单号查询原始开票申请信息
     *
     * @param transactionNumber 平台原始交易单号
     * @return 原始开票申请信息
     */
    public OriginalInvoice selectOriginalInvoiceByTransactionNumber(String transactionNumber) {
        return originalInvoiceBizService.selectOriginalInvoiceByTransactionNumber(transactionNumber).orElseThrow(() -> new BizDataNotFoundException(MessageConst.DATA_NOT_FOUND_CODE, MessageConst.DATA_NOT_FOUND_CONTENT));
    }

    /**
     * 淘宝手工拉取
     *
     * @param req 淘宝手工拉取请求信息
     * @return 批量操作返回信息
     */
    public BatchOperationGenericRes<BatchOperateOriginalInvoiceRes> manualPullTbInvoiceApply(TbManualPullInvoiceReq req) {
        BatchOperationGenericRes<BatchOperateOriginalInvoiceRes> batchOperationRes = new BatchOperationGenericRes<>();
        LocalDateTime now = LocalDateTime.now();
        String operateTime = formatDate(now, FMT_sdf17);
        Long unixTime = formatUnixTime(now);
        int successCount = 0;
        int failCount = 0;
        Map<String, Object> successIdMap = new HashMap<>();
        List<BatchOperateOriginalInvoiceRes> batchOperateRes = new ArrayList<>();
        try {
            MatrixResult<InvoiceApplyGetResponse> result = invoicingBiz.invoiceApplyGet(getInvoiceApplyGetReq(req, null, operateTime));
            if (result.getSuccess()) {
                List<InvoiceApplyDetail> invoiceApplyDetails = result.getData().getApply();
                if (!CollectionUtils.isEmpty(invoiceApplyDetails)) {
                    //异步下载原始开票申请信息
                    asyncBiz.asyncDownInvoiceApply(invoiceApplyDetails, req.getOperator());
                    successCount = invoiceApplyDetails.size();
                }
                batchOperateRes.add(new BatchOperateOriginalInvoiceRes(req.getTransactionNumber(), String.format(MessageConst.CURRENT_INPUT_ALREADY_OPERATE_CONTENT, "平台订单号", "未拉取到原始开票申请信息"), req.getTransactionNumber()));
                log.debug("原始开票申请手工拉取(transactionNumber : {})失败,原因是未拉取到原始开票申请信息。", req.getTransactionNumber());
            } else {
                batchOperateRes.add(new BatchOperateOriginalInvoiceRes(req.getTransactionNumber(), String.format(MessageConst.CURRENT_INPUT_ALREADY_OPERATE_CONTENT, "手工拉取", "原始开票申请信息失败"), req.getTransactionNumber()));
                log.debug("原始开票申请手工拉取(transactionNumber : {})失败,原因是未查询到原始开票申请信息。", req.getTransactionNumber());
                failCount++;
            }
        } catch (Exception e) {
            String errorMsg = MessageConst.SYSTEM_ERROR_CONTENT;
            if (e instanceof BizServiceException) {
                errorMsg = ((BizServiceException) e).getErrorMessage();
            }
            batchOperateRes.add(new BatchOperateOriginalInvoiceRes(req.getTransactionNumber(), errorMsg, req.getTransactionNumber()));
            log.warn("原始开票申请手工拉取(code : {})失败,原因是: {}", req.getTransactionNumber(), errorMsg);
            failCount++;
        }
        // 构造结果并返回
        List<ErrorDataTitle> errorDataTitles = new ArrayList<>();
        // 设置失败列表标题
        errorDataTitles.add(new ErrorDataTitle("平台原始交易单号", "transactionNumber"));
        // 成功数
        batchOperationRes.setSuccessCount(successCount);
        // 失败数
        batchOperationRes.setFailCount(failCount);
        batchOperationRes.setErrorDataTitles(errorDataTitles);
        // 失败原因
        batchOperationRes.setErrorDetailsList(batchOperateRes);
        try {
            if (MapUtils.isNotEmpty(successIdMap)) {
                RecordContextHolder.createRecordContext(new RecordContext<>(successIdMap, req.getOperator(), operateTime, unixTime));
            }
        } catch (Exception e) {
            log.warn("原始开票申请手工拉取添加操作日志失败: successIds : {}", successIdMap.keySet(), e);
        }

        return batchOperationRes;
    }

    /**
     * 抖音手工拉取
     *
     * @param req 抖音手工拉取请求信息
     * @return 批量操作返回信息
     */
    public BatchOperationGenericRes<BatchDyPullOriginalInvoiceRes> manualPullDyInvoiceApply(DyManualPullInvoiceReq req) {
        BatchOperationGenericRes<BatchDyPullOriginalInvoiceRes> batchOperationRes = new BatchOperationGenericRes<>();
        LocalDateTime now = LocalDateTime.now();
        String operateTime = formatDate(now, FMT_sdf17);
        Long unixTime = formatUnixTime(now);
        int successCount = 0;
        int failCount = 0;
        Map<String, Object> successIdMap = new HashMap<>();
        List<BatchDyPullOriginalInvoiceRes> batchOperateRes = new ArrayList<>();
        try {
            MatrixResult<InvoiceApplyGetResponse> result = invoicingBiz.invoiceApplyGet(getInvoiceApplyGetReq(null, req, operateTime));
            if (result.getSuccess()) {
                List<InvoiceApplyDetail> invoiceApplyDetails = result.getData().getApply();
                if (!CollectionUtils.isEmpty(invoiceApplyDetails)) {
                    //异步下载原始开票申请信息
                    asyncBiz.asyncDownInvoiceApply(invoiceApplyDetails, req.getOperator());
                    successCount = invoiceApplyDetails.size();
                }
                batchOperateRes.add(new BatchDyPullOriginalInvoiceRes(req.getStoreCode(), String.format(MessageConst.CURRENT_INPUT_ALREADY_OPERATE_CONTENT, "店铺", "未拉取到原始开票申请信息"), req.getStoreName()));
                log.debug("原始开票申请手工拉取(storeCode : {})失败,原因是未拉取到原始开票申请信息。", req.getStoreCode());
            } else {
                batchOperateRes.add(new BatchDyPullOriginalInvoiceRes(req.getStoreCode(), String.format(MessageConst.CURRENT_INPUT_ALREADY_OPERATE_CONTENT, "手工拉取", "原始开票申请信息失败"), req.getStoreName()));
                log.debug("原始开票申请手工拉取(storeCode : {})失败,原因是未查询到原始开票申请信息。", req.getStoreCode());
                failCount++;
            }
        } catch (Exception e) {
            String errorMsg = MessageConst.SYSTEM_ERROR_CONTENT;
            if (e instanceof BizServiceException) {
                errorMsg = ((BizServiceException) e).getErrorMessage();
            }
            batchOperateRes.add(new BatchDyPullOriginalInvoiceRes(req.getStoreCode(), errorMsg, req.getStoreName()));
            log.warn("原始开票申请手工拉取(code : {})失败,原因是: {}", req.getStoreCode(), errorMsg);
            failCount++;
        }
        // 构造结果并返回
        List<ErrorDataTitle> errorDataTitles = new ArrayList<>();
        // 设置失败列表标题
        errorDataTitles.add(new ErrorDataTitle("店铺名称", "storeName"));
        // 成功数
        batchOperationRes.setSuccessCount(successCount);
        // 失败数
        batchOperationRes.setFailCount(failCount);
        batchOperationRes.setErrorDataTitles(errorDataTitles);
        // 失败原因
        batchOperationRes.setErrorDetailsList(batchOperateRes);
        try {
            if (MapUtils.isNotEmpty(successIdMap)) {
                RecordContextHolder.createRecordContext(new RecordContext<>(successIdMap, req.getOperator(), operateTime, unixTime));
            }
        } catch (Exception e) {
            log.warn("原始开票申请手工拉取添加操作日志失败: successIds : {}", successIdMap.keySet(), e);
        }

        return batchOperationRes;
    }


    /**
     * 原始开票申请手工转换
     *
     * @param req 手工转换信息
     * @return 批量操作返回信息
     */
    public BatchOperationGenericRes<BatchOperateOriginalInvoiceRes> convertInvoiceApply(BatchConvertInvoiceReq req) {
        BatchOperationGenericRes<BatchOperateOriginalInvoiceRes> batchOperationRes = new BatchOperationGenericRes<>();
        LocalDateTime now = LocalDateTime.now();
        String operateTime = formatDate(now, FMT_sdf17);
        Long unixTime = formatUnixTime(now);
        int successCount = 0;
        int failCount = 0;
        Map<String, Object> successIdMap = new HashMap<>();
        List<BatchOperateOriginalInvoiceRes> batchOperateRes = new ArrayList<>();
        for (String transactionNumber : req.getTransactionNumbers()) {
            try {
                OriginalInvoice originalInvoice = originalInvoiceBizService.selectOriginalInvoiceByTransactionNumber(transactionNumber).orElse(null);
                if (originalInvoice == null) {
                    batchOperateRes.add(new BatchOperateOriginalInvoiceRes(transactionNumber, MessageConst.DATA_NOT_FOUND_CONTENT, transactionNumber));
                    log.debug("原始开票申请手工转换(transactionNumber : {})失败,原因是未查询到原始开票申请信息。", transactionNumber);
                    failCount++;
                    continue;
                }
                if (Objects.equals(CommonConst.CONVERT_STATUS_ALREADY, originalInvoice.getConversionStatus())
                        || Objects.equals(CommonConst.CONVERT_STATUS_MIDDLE, originalInvoice.getConversionStatus())) {
                    batchOperateRes.add(new BatchOperateOriginalInvoiceRes(transactionNumber, String.format(MessageConst.CURRENT_INPUT_ALREADY_OPERATE_CONTENT, "原始开票申请", "状态为已转换或转换中"), transactionNumber));
                    log.debug("原始开票申请手工转换(transactionNumber : {})失败,原因是当前原始开票申请状态为已转换或转换中。", transactionNumber);
                    failCount++;
                    continue;
                }
                // 发送MQ给财务中心

                InvoiceApplyDto invoiceApplyDto = assignmentInvoiceApplyDto(originalInvoice);

                if (CommonConst.SOURCE_PLATFORM_DY.equals(invoiceApplyDto.getPlatformType())) {
                    if (DouYinInvoiceStatusEnum.INVOICED.getCode().equals(invoiceApplyDto.getStatus())
                            || DouYinInvoiceStatusEnum.CLOSED.getCode().equals(invoiceApplyDto.getStatus())) {
                        batchOperateRes.add(new BatchOperateOriginalInvoiceRes(transactionNumber, String.format(MessageConst.CURRENT_INPUT_ALREADY_OPERATE_CONTENT, "原始开票申请", "开票状态不为待开票"), transactionNumber));
                        failCount++;
                        continue;
                    }
                } else {
                    /**
                    InvoiceApplyItemDto invoiceItems = invoiceApplyDto.getInvoiceItems();
                    if (Objects.nonNull(invoiceItems)) {
                        List<InvoiceApplyItem> invoiceItem = invoiceItems.getInvoiceItem();
                        if (CollectionUtils.isNotEmpty(invoiceItem)) {
                            List<InvoiceApplyItem> newInvoiceItem = Lists.newArrayList();
                            invoiceItems.setInvoiceItem(newInvoiceItem);
                            for (InvoiceApplyItem item : invoiceItem) {
                                //排除掉折扣行
                                if (CommonConst.BW_INVOICE_ROW_TYPE_DISCOUNT.equals(item.getRowType())) {
                                    continue;
                                }
                                InvoiceApplyItem newInvoiceItems = new InvoiceApplyItem();
                                BeanUtils.copyProperties(item, newInvoiceItems);
                                newInvoiceItem.add(newInvoiceItems);
                            }
                        }
                    }*/
                }
                //this.sendMq(applicationMqProperties.getOrderInvoiceApply(), MessageQueueConst.MQ_BIZ_TYPE_ORDER_INVOICE_APPLY, invoiceApplyDto);
                originalInvoiceCommonService.sendOriginalInvoice2FinanceMq(invoiceApplyDto);
                successIdMap.put(transactionNumber, null);
                successCount++;
            } catch (Exception e) {
                log.warn("原始开票申请手工转换异常", e);
                String errorMsg = MessageConst.SYSTEM_ERROR_CONTENT;
                if (e instanceof JsonProcessingException) {
                    errorMsg = String.format(MessageConst.CURRENT_INPUT_ALREADY_OPERATE_CONTENT, "原始开票申请", "详细信息格式不正确");
                }
                batchOperateRes.add(new BatchOperateOriginalInvoiceRes(transactionNumber, errorMsg, transactionNumber));
                log.warn("原始开票申请手工转换(code : {})失败,原因是: {}", transactionNumber, errorMsg);
                failCount++;
            }
        }
        // 构造结果并返回
        List<ErrorDataTitle> errorDataTitles = new ArrayList<>();
        // 设置失败列表标题
        errorDataTitles.add(new ErrorDataTitle("平台原始交易单号", "transactionNumber"));
        // 成功数
        batchOperationRes.setSuccessCount(successCount);
        // 失败数
        batchOperationRes.setFailCount(failCount);
        batchOperationRes.setErrorDataTitles(errorDataTitles);
        // 失败原因
        batchOperationRes.setErrorDetailsList(batchOperateRes);
        try {
            if (MapUtils.isNotEmpty(successIdMap)) {
                RecordContextHolder.createRecordContext(new RecordContext<>(successIdMap, req.getOperator(), operateTime, unixTime));
            }
        } catch (Exception e) {
            log.warn("原始开票申请手工转换添加操作日志失败: successIds : {}", successIdMap.keySet(), e);
        }

        return batchOperationRes;
    }

    /**
     * 获取开票申请信息接口请求实体
     *
     * @param tbReq       淘宝拉取信息
     * @param dyReq       抖音拉取信息
     * @param operateTime 操作时间
     * @return 开票申请信息接口请求实体
     */
    private InvoiceApplyGetReq getInvoiceApplyGetReq(TbManualPullInvoiceReq tbReq, DyManualPullInvoiceReq dyReq, String operateTime) {
        InvoiceApplyGetReq req = new InvoiceApplyGetReq();
        //淘宝
        if (!Objects.isNull(tbReq)) {
            RetailOrderDto retailOrderDto = otherCenterDataService.getRetailOrderByPlatformOrderNumber(tbReq.getTransactionNumber());
            if (Objects.isNull(retailOrderDto)) {
                throw new BizDataNotFoundException(MessageConst.CURRENT_INPUT_ALREADY_OPERATE_CODE, String.format(MessageConst.CURRENT_INPUT_ALREADY_OPERATE_CONTENT, "平台订单号未查询到", "零售发货单"));
            }
            RedundantChannel redundantChannel = otherCenterDataService.getChannelRedundantData(retailOrderDto.getOnlineStoreCode(), CHANNEL_TYPE_ONLINE);
            if (Objects.isNull(redundantChannel)) {
                throw new BizDataNotFoundException(MessageConst.CURRENT_INPUT_ALREADY_OPERATE_CODE, String.format(MessageConst.CURRENT_INPUT_ALREADY_OPERATE_CONTENT, "零售订单", "未查询到店铺冗余信息"));
            }
            req.setToNodeId(redundantChannel.getSystemNode());
            req.setNodeType(CommonConst.NODE_TYPE_TAOBAO);
            req.setPlatformTid(tbReq.getTransactionNumber());
            req.setApplyId(StringUtils.EMPTY);
            req.setDate(operateTime);
        }
        //抖音
        if (!Objects.isNull(dyReq)) {
            RedundantChannel redundantChannel = otherCenterDataService.getChannelRedundantData(dyReq.getStoreCode(), CHANNEL_TYPE_ONLINE);
            if (Objects.isNull(redundantChannel)) {
                throw new BizDataNotFoundException(MessageConst.CURRENT_INPUT_ALREADY_OPERATE_CODE, String.format(MessageConst.CURRENT_INPUT_ALREADY_OPERATE_CONTENT, "店铺编码", "未查询到店铺冗余信息"));
            }
            req.setToNodeId(redundantChannel.getSystemNode());
            req.setNodeType(CommonConst.NODE_TYPE_DY);
            if (StringUtils.isNotBlank(dyReq.getTransactionNumber())) {
                req.setPlatformTid(dyReq.getTransactionNumber());
            }
            if (StringUtils.isNotBlank(dyReq.getInvoicingStatus())) {
                req.setStatus(dyReq.getInvoicingStatus());
            }
            if (dyReq.getStartTime() != null) {
                req.setStartTime(String.valueOf(dyReq.getStartTime()));
            }
            if (dyReq.getEndTime() != null) {
                req.setEndTime(String.valueOf(dyReq.getEndTime()));
            }
            req.setApplyId(StringUtils.EMPTY);
            req.setDate(operateTime);
        }
        return req;

    }

    /**
     * 获取原始开票申请转换实体
     *
     * @param originalInvoice 原始开票申请
     * @return 原始开票申请转换实体
     * @throws JsonProcessingException
     */
    private InvoiceApplyDto assignmentInvoiceApplyDto(OriginalInvoice originalInvoice) throws JsonProcessingException {
        InvoiceApplyDto dto = new InvoiceApplyDto();
        //原始开票申请详细信息不为空
        if (!Objects.isNull(originalInvoice) && StringUtils.isNotBlank(originalInvoice.getInvoiceDetails())) {
            dto.setPlatformType(originalInvoice.getSourcePlatform());
            dto.setStoreCode(originalInvoice.getStoreCode());
           /* if (Objects.equals(CommonConst.STORE_TYPE_JD, originalInvoice.getSourcePlatform())) {
                dto = objectMapper.readValue(originalInvoice.getInvoiceDetails(), InvoiceApplyDto.class);
            } else {
                Map<String, Object> invoiceInfo = this.objectMapper.readValue(originalInvoice.getInvoiceDetails(), new TypeReference<Map<String, Object>>() {
                });
                // 转换原始开票申请信息为通用实体
                OriginalInvoiceConversion.createInvoiceApplyDto(dto, invoiceInfo);
            }*/
            try {
                InvoiceApplyDto invoiceApplyDto = objectMapper.readValue(originalInvoice.getInvoiceDetails(), InvoiceApplyDto.class);
                invoiceApplyDto.setPlatformType(originalInvoice.getSourcePlatform());
                invoiceApplyDto.setStoreCode(originalInvoice.getStoreCode());
                return invoiceApplyDto;
            } catch (JsonProcessingException e) {
                log.warn("手工转换 -- 发票转换json反序列化失败");
                throw new BizServiceException(MessageConst.BW_INVOICE_PARAM_SERIALIZE_ERROR, MessageConst.BW_INVOICE_PARAM_SERIALIZE_ERROR_CONTENT);
            }

        }
        return dto;
    }

    /**
     * 发送MQ消息
     *
     * @param topic     topic
     * @param bizType   业务类型
     * @param orderInfo 原始订单信息
     */
    private void sendMq(ApplicationMqProperties.Topic topic, String bizType, Object orderInfo) {
        log.info("原始开票申请手工转换 发送转换MQ消息 start topic: {}, bizType: {}, orderInfo: {}", topic, bizType, orderInfo);
        try {
            this.rmqCommProducer.sendSyncMsg(topic.getTopic(),
                    topic.getTag(), new RMQProducerBody(bizType, orderInfo));
        } catch (Exception e) {
            log.warn("原始开票申请手工转换 发送转换MQ消息异常 topic: {}, bizType: {}", topic, bizType, e);
        }
        log.info("原始开票申请手工转换 发送转换MQ消息 end topic: {}, bizType: {}", topic, bizType);
    }

}
