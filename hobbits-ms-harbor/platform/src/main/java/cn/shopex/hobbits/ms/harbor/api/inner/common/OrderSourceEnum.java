package cn.shopex.hobbits.ms.harbor.api.inner.common;

import cn.shopex.hobbits.ms.harbor.common.CommonConst;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Getter
public enum OrderSourceEnum {
    /**
     * 店铺对应订单来源平台
     */
    JD(0, "JD", "京东"),
    TB(1, "TB", "淘宝"),
    DY(2, "DY", "抖音"),
    PDD(3, "PDD", "拼多多"),
    XHS(5, "XHS", "小红书"),
    WPH(6, "WPH", "唯品会"),
    KS(11, "KS", "快手"),
    DEWU(12, "DEWU", "得物"),
    SI_PRIVATE(999999, "SI_PRIVATE", "私域平台"),
    WXSPH(10, "WXSPH", "微信小店"),


    OTHER(999, "OTHER", "其他"),
    ;


    private final Integer storeType;
    private final String orderSource;
    private final String orderSourceName;

    OrderSourceEnum(Integer storeType, String orderSource, String orderSourceName) {
        this.storeType = storeType;
        this.orderSource = orderSource;
        this.orderSourceName = orderSourceName;
    }

    /**
     * 根据店铺类型获取订单来源平台信息
     *
     * @return 订单来源平台信息
     */
    public static OrderSourceEnum getOrderSourceEnumByStoreType(Integer storeType) {
        if (storeType >= CommonConst.SOURCE_PLATFORM_EZR) {
            return SI_PRIVATE;
        }
        for (OrderSourceEnum orderSourceEnum : OrderSourceEnum.values()) {
            if (Objects.equals(storeType, orderSourceEnum.getStoreType())) {
                return orderSourceEnum;
            }
        }
        return OTHER;
    }

}
