# 所属集群名字
brokerClusterName = DefaultCluster

# broker名字，注意此处不同的配置文件填写的不一样
brokerName = broker-a

# 0 表示 Master，>0 表示 Slave
brokerId = 0

# nameServer地址，分号分割
namesrvAddr = rocketmq-nameserver:9876

# 删除文件时间点，默认凌晨 4点
deleteWhen = 04

# 文件保留时间，默认 48 小时
fileReservedTime = 48

# Broker 的角色
# ASYNC_MASTER 异步复制Master
# SYNC_MASTER 同步双写Master
# SLAVE
brokerRole = ASYNC_MASTER

# 刷盘方式
# ASYNC_FLUSH 异步刷盘
# SYNC_FLUSH 同步刷盘
flushDiskType = ASYNC_FLUSH

# 是否允许Broker 自动创建Topic，建议线下开启，线上关闭
autoCreateTopicEnable = true

# 是否允许Broker 自动创建订阅组，建议线下开启，线上关闭
autoCreateSubscriptionGroup = true

# Broker对外服务的监听端口
listenPort = 10911

# Broker服务地址
brokerIP1 = rocketmq-broker

# 存储路径
storePathRootDir = /home/<USER>/store
storePathCommitLog = /home/<USER>/store/commitlog
storePathConsumeQueue = /home/<USER>/store/consumequeue
storePathIndex = /home/<USER>/store/index
storeCheckpoint = /home/<USER>/store/checkpoint
abortFile = /home/<USER>/store/abort

# 消息大小限制
maxMessageSize = 65536

# 线程池配置
defaultThreadPoolNums = 16
clientThreadPoolNums = 16
consumerThreadPoolNums = 16
pullMessageThreadPoolNums = 16
queryMessageThreadPoolNums = 8
adminBrokerThreadPoolNums = 8
sendThreadPoolNums = 16