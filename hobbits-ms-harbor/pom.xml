<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>
    <modules>
        <module>orm</module>
        <module>platform</module>
    </modules>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.5.14</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <groupId>cn.shopex.hobbits.ms</groupId>
    <artifactId>hobbits-ms-harbor-parent</artifactId>

    <name>Hobbits Microservice Interface Center</name>
    <description>微服务接口中心</description>

    <version>${revision}</version>

    <properties>
        <revision>2.30.1</revision>
        <java.version>1.8</java.version>
        <spring-cloud.version>2020.0.3</spring-cloud.version>
        <swagger.version>3.0.0</swagger.version>
        <mybatis-spring-boot.version>2.2.0</mybatis-spring-boot.version>
        <druid-spring-boot.version>1.2.6</druid-spring-boot.version>
        <guava.version>31.0.1-jre</guava.version>
        <micrometer-registry-prometheus.version>1.8.0</micrometer-registry-prometheus.version>
        <micrometer-jvm-extras.version>0.2.2</micrometer-jvm-extras.version>
        <assertj-core.version>3.21.0</assertj-core.version>
        <hobbits-ms-library.version>0.8.54</hobbits-ms-library.version>
        <neb.version>0.5.18</neb.version>
        <hobbits-lib-toolkit-rmqtoolkit.version>0.1.5</hobbits-lib-toolkit-rmqtoolkit.version>
        <rocketmq-spring-boot.version>2.2.1</rocketmq-spring-boot.version>
        <redisson-spring-boot-starter.version>3.16.4</redisson-spring-boot-starter.version>
        <xxl-job-core.version>2.3.3-hobbits</xxl-job-core.version>
        <commons-text.version>1.9</commons-text.version>
        <commons-io.version>2.11.0</commons-io.version>
        <charon.version>0.2.4</charon.version>
        <hutool.version>5.8.25</hutool.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>cn.shopex.hobbits.ms</groupId>
            <artifactId>hobbits-ms-library</artifactId>
            <version>${hobbits-ms-library.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-elasticsearch</artifactId>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-boot-starter</artifactId>
            <version>${swagger.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>${mybatis-spring-boot.version}</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>${druid-spring-boot.version}</version>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>${hutool.version}</version>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <!-- Spring Cloud -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- Hobbits Common Library -->
            <dependency>
                <groupId>cn.shopex.hobbits.lib</groupId>
                <artifactId>hobbits-lib-toolkit-rmqtoolkit</artifactId>
                <version>${hobbits-lib-toolkit-rmqtoolkit.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-spring-boot-starter</artifactId>
                <version>${rocketmq-spring-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.shopex.hobbits</groupId>
                <artifactId>neb-springboot-sdk</artifactId>
                <version>${neb.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.shopex.hobbits</groupId>
                <artifactId>charon-springboot-sdk</artifactId>
                <version>${charon.version}</version>
            </dependency>
            <!-- Apache & Google Utility -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <!-- Prometheus -->
            <dependency>
                <groupId>io.micrometer</groupId>
                <artifactId>micrometer-registry-prometheus</artifactId>
                <version>${micrometer-registry-prometheus.version}</version>
            </dependency>
            <dependency>
                <groupId>io.github.mweirauch</groupId>
                <artifactId>micrometer-jvm-extras</artifactId>
                <version>${micrometer-jvm-extras.version}</version>
            </dependency>
            <!-- JUnit test -->
            <dependency>
                <groupId>org.assertj</groupId>
                <artifactId>assertj-core</artifactId>
                <version>${assertj-core.version}</version>
                <scope>test</scope>
            </dependency>
            <!-- Redisson -->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson-spring-boot-starter.version}</version>
            </dependency>
            <!-- xxl-component-core -->
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job-core.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-text</artifactId>
                <version>${commons-text.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

</project>
