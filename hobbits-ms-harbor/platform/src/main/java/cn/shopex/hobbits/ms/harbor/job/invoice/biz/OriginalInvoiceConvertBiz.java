package cn.shopex.hobbits.ms.harbor.job.invoice.biz;

import cn.shopex.hobbits.ms.harbor.common.CommonConst;
import cn.shopex.hobbits.ms.harbor.common.MessageConst;
import cn.shopex.hobbits.ms.harbor.common.MessageQueueConst;
import cn.shopex.hobbits.ms.harbor.common.domain.order.InvoiceApplyDto;
import cn.shopex.hobbits.ms.harbor.configuration.ApplicationMqProperties;
import cn.shopex.hobbits.ms.harbor.web.order.invoice.enums.DouYinInvoiceStatusEnum;
import cn.shopex.hobbits.ms.harbor.web.order.invoice.mapper.OriginalInvoiceBizMapper;
import cn.shopex.hobbits.ms.harbor.web.order.invoice.service.OriginalInvoiceCommonService;
import cn.shopex.hobbits.ms.library.exception.BizServiceException;
import cn.shopex.hobbits.ms.orm.model.OriginalInvoice;
import cn.shopex.hobbits.ms.rmp.biz.producer.RMQCommProducer;
import cn.shopex.hobbits.ms.rmp.biz.producer.domain.RMQProducerBody;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

@Component
@RequiredArgsConstructor
@Slf4j
public class OriginalInvoiceConvertBiz {
    private final OriginalInvoiceBizMapper originalInvoiceBizMapper;
    private final ApplicationMqProperties applicationMqProperties;
    private final RMQCommProducer rmqCommProducer;
    private final ObjectMapper objectMapper;
    private final OriginalInvoiceCommonService originalInvoiceCommonService;

    public void originalInvoiceConvert() {
        List<OriginalInvoice> originalInvoices = originalInvoiceBizMapper.selectCountOriginalInvoiceByConversionStatus(CommonConst.CONVERT_STATUS_FAIL);
        if (CollectionUtils.isEmpty(originalInvoices)) {
            log.info("没有需要转换的发票数据");
            return;
        }
        for (OriginalInvoice originalInvoice : originalInvoices) {
            if(originalInvoice.getRetryTimes() >= CommonConst.FAIL_RETRY_TIMES){
                continue;
            }
            InvoiceApplyDto invoiceApplyDto = assignmentInvoiceApplyDto(originalInvoice);
//            if (CommonConst.SOURCE_PLATFORM_DY.equals(invoiceApplyDto.getPlatformType())) {
//                if (DouYinInvoiceStatusEnum.INVOICED.getCode().equals(invoiceApplyDto.getStatus())
//                        || DouYinInvoiceStatusEnum.CLOSED.getCode().equals(invoiceApplyDto.getStatus())) {
//                    continue;
//                }
//            }
//            this.sendMq(applicationMqProperties.getOrderInvoiceApply(), MessageQueueConst.MQ_BIZ_TYPE_ORDER_INVOICE_APPLY, invoiceApplyDto);
            originalInvoiceCommonService.sendOriginalInvoice2FinanceMq(invoiceApplyDto);
        }
    }

//    private void sendMq(ApplicationMqProperties.Topic topic, String bizType, InvoiceApplyDto orderInfo) {
//        log.info("原始开票申请定时任务 发送转换MQ消息 start topic: {}, bizType: {}, orderInfo: {}", topic, bizType, orderInfo);
//        if (Objects.isNull(orderInfo)) {
//            log.warn("原始开票申请定时任务 原始发票信息转换对象为空");
//            return;
//        }
//        try {
//            this.rmqCommProducer.sendSyncMsg(topic.getTopic(),
//                    topic.getTag(), new RMQProducerBody(bizType, orderInfo));
//        } catch (Exception e) {
//            log.warn("原始开票申请定时任务 发送转换MQ消息异常 topic: {}, bizType: {}", topic, bizType, e);
//        }
//        log.info("原始开票申请定时任务 发送转换MQ消息 end topic: {}, bizType: {}", topic, bizType);
//    }

    private InvoiceApplyDto assignmentInvoiceApplyDto(OriginalInvoice originalInvoice) {
        InvoiceApplyDto dto = new InvoiceApplyDto();
        //原始开票申请详细信息不为空
        if (!Objects.isNull(originalInvoice) && StringUtils.isNotBlank(originalInvoice.getInvoiceDetails())) {
            dto.setPlatformType(originalInvoice.getSourcePlatform());
            dto.setStoreCode(originalInvoice.getStoreCode());
            try {
                InvoiceApplyDto invoiceApplyDto = objectMapper.readValue(originalInvoice.getInvoiceDetails(), InvoiceApplyDto.class);
                invoiceApplyDto.setPlatformType(originalInvoice.getSourcePlatform());
                invoiceApplyDto.setStoreCode(originalInvoice.getStoreCode());
                return invoiceApplyDto;
            } catch (JsonProcessingException e) {
                log.warn("定时任务 -- 发票转换json反序列化失败");
                throw new BizServiceException(MessageConst.BW_INVOICE_PARAM_SERIALIZE_ERROR, MessageConst.BW_INVOICE_PARAM_SERIALIZE_ERROR_CONTENT);
            }
        }
        return dto;
    }
}
