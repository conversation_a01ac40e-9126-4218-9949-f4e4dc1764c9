package cn.shopex.hobbits.ms.harbor.common;

/**
 * <AUTHOR>
 */
public interface CommonConst {
    String REDIS_HASH_MS_ENV = "sys:ms";
    String ELECTRONIC_FACE_SHEET_CAINIAO_USERID_KEY = "electronic_face_sheet_cainiao_userid";
    /**
     * 系统默认用户
     */
    String DEFAULT_OPERATOR = "System";

    /**
     * http header client
     */
    String HEADER_CLIENT = "X-Auth-Client";

    /**
     * http header message-id
     */
    String OPENAPI_HEADER_MESSAGE_ID = "x-message-id";

    /**
     * 来源平台: 京东
     */
    Integer SOURCE_PLATFORM_JD = 0;
    /**
     * 来源平台: 淘宝
     */
    Integer SOURCE_PLATFORM_TB = 1;
    /**
     * 来源平台: 抖音
     */
    Integer SOURCE_PLATFORM_DY = 2;
    /**
     * 来源平台: 拼多多
     */
    Integer SOURCE_PLATFORM_PDD = 3;
    /**
     * 来源平台: 小红书
     */
    Integer SOURCE_PLATFORM_XHS = 5;
    /**
     * 来源平台: 唯品会
     */
    Integer SOURCE_PLATFORM_WPH = 6;
    /**
     * 来源平台: momo
     */
    Integer SOURCE_PLATFORM_MOMO = 7;
    /**
     * 来源平台: shopee
     */
    Integer SOURCE_PLATFORM_SHOPEE = 8;
    /**
     * 来源平台: lazada
     */
    Integer SOURCE_PLATFORM_LAZADA = 9;
    /**
     * 来源平台: 微信小店
     */
    Integer SOURCE_PLATFORM_WECHAT_STORE = 10;
    /**
     * 来源平台: kuaishou
     */
    Integer SOURCE_PLATFORM_KS = 11;
    /**
     * 来源平台: 得物
     */
    Integer SOURCE_PLATFORM_DEWU = 12;
    /**
     * 来源平台: 驿氪
     */
    Integer SOURCE_PLATFORM_EZR = 10000;

    /**
     * 头信息字段：菜单ID
     */
    String HEADER_MENU_ID = "mid";
    /**
     * 删除flag 正常
     */
    Integer COMMON_DELETE_FLAG_ENABLE = 0;
    /**
     * 删除flag 删除
     */
    Integer COMMON_DELETE_FLAG_DISABLE = 1;

    /**
     * 启用状态 启用
     */
    Integer COMMON_STATUS_ENABLE = 0;
    /**
     * 启用状态 禁用
     */
    Integer COMMON_STATUS_DISABLE = 1;

    /**
     * 绑定状态 未绑定
     */
    Integer COMMON_UNBOUND = 0;
    /**
     * 绑定状态 已绑定
     */
    Integer COMMON_BOUND = 1;

    /**
     * 是否状态 是
     */
    Integer COMMON_TRUE = 0;
    /**
     * 是否状态 否
     */
    Integer COMMON_FALSE = 1;

    /**
     * 是否默认: 非默认
     */
    Integer NO_DEFAULT = 1;

    /**
     * 默认排序
     */
    Integer DEFAULT_RANKING = 1;

    /**
     * 失败重试次数
     */
    Integer FAIL_RETRY_TIMES = 3;

    /**
     * 单据状态 待审核
     */
    Integer BILL_STATUS_WAIT = 0;
    /**
     * 单据状态 已审核
     */
    Integer BILL_STATUS_ALREADY = 1;
    /**
     * 单据状态 已取消
     */
    Integer BILL_STATUS_CANCEL = 2;

    /**
     * 转换状态 待转换
     */
    Integer CONVERT_STATUS_WAIT = 10;
    /**
     * 转换状态 已转换
     */
    Integer CONVERT_STATUS_ALREADY = 20;
    /**
     * 转换状态 转换失败
     */
    Integer CONVERT_STATUS_FAIL = 30;
    /**
     * 转换状态 转换中
     */
    Integer CONVERT_STATUS_MIDDLE = 40;

    /**
     * 业务节点 接单失败
     */
    Integer BUSINESS_NODE_ORDER_FAIL = 0;

    /**
     * 业务节点 已接单
     */
    Integer BUSINESS_NODE_ORDER_SUCCESS = 1;

    /**
     * 业务节点 部分出库
     */
    Integer BUSINESS_NODE_PART_OUT = 11;

    /**
     * 业务节点 全部出库
     */
    Integer BUSINESS_NODE_ALL_OUT = 12;

    /**
     * 业务节点 部分入库
     */
    Integer BUSINESS_NODE_PART_IN = 21;

    /**
     * 业务节点 全部入库
     */
    Integer BUSINESS_NODE_ALL_IN = 22;

    /**
     * 业务节点 已发货
     */
    Integer BUSINESS_NODE_DELIVER = 31;

    /**
     * 业务节点 发货完成
     */
    Integer BUSINESS_NODE_PRINT = 32;

    /**
     * 业务节点 待自提
     */
    Integer BUSINESS_NODE_PICKUP = 33;

    /**
     * 业务节点 收货完成
     */
    Integer BUSINESS_NODE_RECEIVING = 41;

    /**
     * 业务节点 拒单
     */
    Integer BUSINESS_NODE_DELIVERY_REFUSE = 92;

    /**
     * 业务节点 未开始处理
     */
    Integer BUSINESS_NODE_GO_OUT_NEW = 0;
    /**
     * 业务节点 仓库接单
     */
    Integer BUSINESS_NODE_GO_OUT_ACCEPT = 1;
    /**
     * 业务节点 部分发货完成
     */
    Integer BUSINESS_NODE_GO_OUT_RECEIVING = 41;
    /**
     * 业务节点 全部发货完成
     */
    Integer BUSINESS_NODE_GO_OUT_ALL_RECEIVING = 42;
    /**
     * 业务节点 部分收货完成
     */
    Integer BUSINESS_NODE_GO_OUT_DELIVER = 31;
    /**
     * 业务节点 全部收货完成
     */
    Integer BUSINESS_NODE_GO_OUT_ALL_DELIVER = 32;
    /**
     * 业务节点 出入库关闭
     */
    Integer BUSINESS_NODE_GO_OUT_CLOSE = 91;
    /**
     * 业务节点 出入库拒单
     */
    Integer BUSINESS_NODE_GO_OUT_REJECT = 92;
    /**
     * 业务节点 出入库取消
     */
    Integer BUSINESS_NODE_GO_OUT_CANCEL = 94;
    /**
     * 业务节点 出入库取消失败
     */
    Integer BUSINESS_NODE_GO_OUT_CANCEL_FAIL = 95;
    /**
     * 业务节点 出入库异常
     */
    Integer BUSINESS_NODE_GO_OUT_EXCEPTION = 99;
    /**
     * 业务节点 拒绝
     */
    Integer BUSINESS_NODE_REFUSE = 97;

    /**
     * 业务节点 取消
     */
    Integer BUSINESS_NODE_CANCEL = 98;

    /**
     * 业务节点 关闭
     */
    Integer BUSINESS_NODE_CLOSE = 99;

    /**
     * 转换失败次数
     */
    Integer CONVERT_STATUS_FAIL_TIMES = 3;

    /**
     * 出入库单据状态 待审核
     */
    Integer STOCK_OUT_IN_BILL_STATUS_WAIT = 1;

    /**
     * 出入库单据状态 已审核
     */
    Integer STOCK_OUT_IN_BILL_STATUS_ALREADY = 2;

    /**
     * 出入库单据状态 已取消
     */
    Integer STOCK_OUT_IN_BILL_STATUS_CANCEL = 3;

    /**
     * 出入库单据状态 已完成
     */
    Integer STOCK_OUT_IN_BILL_STATUS_DONE = 4;

    /**
     * 调拨出入库单据状态 待处理
     */
    Integer ALLOT_STOCK_OUT_IN_BILL_STATUS_WAIT = 0;

    /**
     * 调拨出入库单据状态 处理中
     */
    Integer ALLOT_STOCK_OUT_IN_BILL_STATUS_DOING = 1;

    /**
     * 调拨出入库单据状态 已完成
     */
    Integer ALLOT_STOCK_OUT_IN_BILL_STATUS_DONE = 2;

    /**
     * 调拨出入库单据状态 已取消
     */
    Integer ALLOT_STOCK_OUT_IN_BILL_STATUS_CANCEL = 3;

    /**
     * 出库结果来源业务类型 一般出库
     */
    Integer STOCK_OUT_SOURCE_TYPE_GENERAL = 150;

    /**
     * 出库结果来源业务类型 调拨出库
     */
    Integer STOCK_OUT_SOURCE_TYPE_ALLOT = 151;

    /**
     * 出库结果来源业务类型 采购出库
     */
    Integer STOCK_OUT_SOURCE_TYPE_PURCHASE = 152;

    /**
     * 入库结果来源业务类型 一般入库
     */
    Integer STOCK_IN_SOURCE_TYPE_GENERAL = 100;

    /**
     * 入库结果来源业务类型 调拨入库
     */
    Integer STOCK_IN_SOURCE_TYPE_ALLOT = 101;

    /**
     * 入库结果来源业务类型 采购入库
     */
    Integer STOCK_IN_SOURCE_TYPE_PURCHASE = 102;

    /**
     * 发货结果单来源业务类型 一般交易出库
     */
    Integer DELIVERY_SHEET_SOURCE_TYPE_GENERAL = 250;

    /**
     * 货款方式 款到发货
     */
    Integer PAYMENT_METHOD_PAYMENT_TO_SHIPMENT = 1;
    /**
     * 货款方式 货到付款
     */
    Integer PAYMENT_METHOD_CASH_ON_DELIVERY = 2;

    /**
     * 订单类型 普通订单
     */
    Integer ORDER_TYPE_ORDINARY = 1;
    /**
     * 订单类型: 预售订单
     */
    Integer ORDER_TYPE_PRE = 2;
    /**
     * 订单类型: 一件代发订单
     */
    Integer ORDER_TYPE_DROPSHIPPING = 6;

    /**
     * 付款状态: 全额支付
     */
    Integer PAYMENT_STATUS_FULL = 3;

    /**
     * 原始订单模板类型: 原始订单
     */
    Integer TEMPLATE_TYPE_ORIGINAL = 1;

    /**
     * 零售发货单状态: 备货中
     */
    Integer RETAIL_INVOICE_STATUS_STOCK_UP = 60;

    /**
     * 计量单位类型: 质量
     */
    Integer MEASUREMENT_TYPE_QUALITY = 1;

    /**
     * 计量单位类型: 长度
     */
    Integer MEASUREMENT_TYPE_LENGTH = 0;

    /**
     * 计量单位类型: 体积
     */
    Integer MEASUREMENT_TYPE_VOLUME = 2;


    /**
     * 分类项目ID：出库结果来源业务类型
     */
    String ITEM_OUTBOUND_SOURCE_TYPE = "inventory_00022";
    /**
     * 分类项目ID：结果单据状态
     */
    String ITEM_BILL_STATUS = "inventory_00023";
    /**
     * 分类项目ID：转换状态
     */
    String ITEM_CONVERT_STATUS = "inventory_00024";
    /**
     * 分类项目ID：入库结果来源业务类型
     */
    String ITEM_GODOWN_SOURCE_TYPE = "inventory_00025";
    /**
     * 分类项目ID：出库结果单业务节点
     */
    String ITEM_OUTBOUND_BUSINESS_NODE = "inventory_00033";
    /**
     * 分类项目ID：入库结果单业务节点
     */
    String ITEM_GODOWN_BUSINESS_NODE = "inventory_00034";
    /**
     * 分类项目ID：发货结果单业务节点
     */
    String ITEM_DELIVERY_BUSINESS_NODE = "inventory_00035";
    /**
     * 分类项目ID：退货结果单业务节点
     */
    String ITEM_RETURN_BUSINESS_NODE = "inventory_00036";

    /**
     * 分类项目ID：发货结果来源业务类型
     */
    String ITEM_DELIVERY_SOURCE_TYPE = "inventory_00026";
    /**
     * 分类项目ID：退货结果来源业务类型
     */
    String ITEM_RETURN_SOURCE_TYPE = "inventory_00027";
    /**
     * 处理方式：自动
     */
    Integer COMMON_15_AUTO = 1;
    /**
     * 处理方式：手动
     */
    Integer COMMON_15_MANUAL = 2;

    /**
     * 退换货单据类型: 退货
     */
    Integer RETURN_CHANGE_RETURN = 1;
    /**
     * 退换货单据类型: 换货
     */
    Integer RETURN_CHANGE_CHANGE = 2;

    /**
     * 退货结果单来源单据类型 退货入库
     */
    Integer RETURN_SHEET_SOURCE_BILL_TYPE_SORT = 200;
    /**
     * 退货结果单来源单据类型 换货入库
     */
    Integer RETURN_SHEET_SOURCE_BILL_TYPE_BARTER = 201;

    /**
     * 组合商品
     */
    Integer MATERIAL_TYPE_COMB = 2;

    /**
     * 订单状态
     * 1.待退货入库
     * 2.待收货确认
     */
    Integer ORDER_STATUS_WAIT_RETURN = 1;
    Integer ORDER_STATUS_WAIT_RECEIVE = 2;

    /**
     * 预售类型: 全款预售
     */
    Integer PRE_SALE_TYPE_FULL_PAYMENT = 1;
    /**
     * 预售类型: 定金预售
     */
    Integer PRE_SALE_TYPE_DEPOSIT = 2;

    /**
     * 矩阵原始单据下载指标
     */
    String MATRIX_CALLBACK_METER_NAME = "hobbits_harbor_matrix_download";

    /**
     * 矩阵原始单据下载指标标签
     */
    String MATRIX_CALLBACK_METER_TAG_TYPE = "type";

    /**
     * 矩阵回调处理标志: 成功
     */
    String MATRIX_CALLBACK_SUCCESS = "succ";
    /**
     * 矩阵回调处理标志: 失败
     */
    String MATRIX_CALLBACK_FAIL = "fail";
    /**
     * 矩阵回调处理成功消息
     */
    String MATRIX_CALLBACK_SUCCESS_RESULT = "the callback is into mq now";

    /**
     * 订单类型（解密）: 平台订单
     */
    Integer RECEIPT_TYPE_ORDER = 1;
    /**
     * 订单类型（解密）: 平台换货单
     */
    Integer RECEIPT_TYPE_ORDER_CHANGE = 2;

    /**
     * 渠道类别 线上
     */
    Integer CHANNEL_TYPE_ONLINE = 0;
    /**
     * 渠道类别 线下
     */
    Integer CHANNEL_TYPE_OFFLINE = 1;

    /**
     * 绑定解绑状态 : 绑定
     */
    String STATUS_BIND = "bind";
    /**
     * 绑定解绑状态 : 解绑
     */
    String STATUS_UNBIND = "unbind";
    /**
     * 仓库类型: 正品仓
     */
    Integer GENUINE_WAREHOUSE = 0;
    /**
     * 仓库类型: 次品仓
     */
    Integer DEFECTIVE_WAREHOUSE = 1;

    /**
     * 库存中心单据状态：处理中
     */
    Integer INVENTORY_BILL_STATUS_PROCESSING = 1;
    /**
     * 库存中心单据状态：已完成
     */
    Integer INVENTORY_BILL_STATUS_FINISH = 2;

    /**
     * 平台发票种类: 电子发票
     */
    Integer PLATFORM_INVOICE_KIND_ELECTRON = 0;
    /**
     * 平台发票种类: 纸质发票
     */
    Integer PLATFORM_INVOICE_KIND_PAPER = 1;
    /**
     * 平台发票种类: 专票
     */
    Integer PLATFORM_INVOICE_KIND_SPECIAL = 2;
    /**
     * 抖音平台开票类型: 增值税普通电子
     */
    Integer DY_PLATFORM_INVOICE_KIND = 1;

    /**
     * 抖音平台开票类型: 数电普通发票
     */
    Integer DY_PLATFORM_INVOICE_KIND_E_NORMAL = 2;

    /**
     * 抖音平台开票类型: 增值税专用发票
     */
    Integer DY_PLATFORM_INVOICE_KIND_SPECIAL = 3;

    /**
     * 抖音平台开票类型: 数电专用发票
     */
    Integer DY_PLATFORM_INVOICE_KIND_E_SPECIAL = 4;



    /**
     * 抖音平台开票类型: 增值税普通电子
     */
    Integer PDD_PLATFORM_INVOICE_KIND = 0;

    /**
     * 抖音平台开票类型: 数电普通发票
     */
    Integer PDD_PLATFORM_INVOICE_KIND_E_NORMAL = 2;

    /**
     * 下发WMS新建状态: 下发成功
     */
    Integer WMS_CREATE_STATUS_SUCCESS = 3;
    /**
     * 下发WMS新建状态: 无需下发
     */
    Integer WMS_CREATE_STATUS_WITHOUT = 5;

    /**
     * 节点类型: 淘宝
     */
    String NODE_TYPE_TAOBAO = "taobao";
    /**
     * 节点类型: 京东
     */
    String NODE_TYPE_JD = "jingdong";
    /**
     * 节点类型: 抖音
     */
    String NODE_TYPE_DY = "luban";
    /**
     * 节点类型: 拼多多
     */
    String NODE_TYPE_PDD = "pinduoduo";
    /**
     * 节点类型: ezr
     */
    String NODE_TYPE_EZR = "ezr";
    /**
     * 节点类型: 小红书
     */
    String NODE_TYPE_XHS = "xiaohongshu";
    /**
     * 节点类型: 唯品会
     */
    String NODE_TYPE_WPH = "vop";
    /**
     * 节点类型: momo
     */
    String NODE_TYPE_MOMO = "momo";
    /**
     * 节点类型: 微信小店
     */
    String NODE_TYPE_WECHAT_STORE = "wxshipin";
    /**
     * 节点类型: 快手
     */
    String NODE_TYPE_KS = "kuaishou";
    /**
     * 节点类型: 得物
     */
    String NODE_TYPE_DEWU = "dewu";
    /**
     * 发货标志:  正常发货
     */
    String PACKAGE_TYPE_NORMAL = "normal";
    /**
     * 发货标志:  拆单发货
     */
    String PACKAGE_TYPE_BREAK = "break";

    /**
     * 是否拆单: 不拆单
     */
    String WHETHER_SPLIT_ORDER_NO = "0";
    /**
     * 是否拆单: 拆单
     */
    String WHETHER_SPLIT_ORDER_YES = "1";

    /**
     * 任务状态 未处理
     */
    Integer TASK_STATUS_WAIT = 0;
    /**
     * 任务状态 处理中
     */
    Integer TASK_STATUS_RUNNING = 1;
    /**
     * 任务状态 处理完成
     */
    Integer TASK_STATUS_SUCCESS = 2;
    /**
     * 任务状态 处理失败
     */
    Integer TASK_STATUS_FAIL = 3;

    /**
     * 盘点单通知库存类型：正品
     */
    String WMS_TAKE_STOCK_TYPE_ZP = "ZP";
    /**
     * 盘点单通知库存类型：残次
     */
    String WMS_TAKE_STOCK_TYPE_CC = "CC";
    /**
     * 盘点单通知库存类型：机损
     */
    String WMS_TAKE_STOCK_TYPE_JS = "JS";
    /**
     * 盘点单通知库存类型：箱损
     */
    String WMS_TAKE_STOCK_TYPE_XS = "XS";
    /**
     * 盘点单通知库存类型：在途
     */
    String WMS_TAKE_STOCK_TYPE_ZT = "ZT";

    /**
     * 盘点单通知：盘点
     */
    String WMS_ADJUST_TYPE_CHECK = "CHECK";
    /**
     * 盘点单通知：调整
     */
    String WMS_ADJUST_TYPE_ADJUST = "ADJUST";

    Integer WMS_ADJUST_TYPE_CHECK_INTVALUE = 1;
    Integer WMS_ADJUST_TYPE_ADJUST_INTVALUE = 2;

    /**
     * 库存类型:正品
     */
    Integer WMS_INVENTORY_TYPE_ZP = 0;
    /**
     * 库存类型:次品
     */
    Integer WMS_INVENTORY_TYPE_CP = 1;
    /**
     * 逻辑仓类型：正品仓
     */
    Integer LOGIC_WAREHOUSE_TYPE_QUALITY = 0;
    /**
     * 逻辑仓类型：次品仓
     */
    Integer LOGIC_WAREHOUSE_TYPE_DEFECTIVE = 1;

    /**
     * 店铺类型: 京东
     */
    int STORE_TYPE_JD = 0;
    /**
     * 店铺类型: 淘宝
     */
    int STORE_TYPE_TAOBAO = 1;
    /**
     * 店铺类型: 抖音
     */
    int STORE_TYPE_DOUYIN = 2;
    /**
     * 店铺类型: 小红书
     */
    int STORE_TYPE_XIAOHONGSHU = 5;

    int STORE_TYPE_PDD = 3;
    /**
     * 店铺类型: 快手
     */
    int STORE_TYPE_KS = 11;
    /**
     * 店铺类型: 得物
     */
    int STORE_TYPE_DEWU = 12;
    /**
     * 店铺类型: lazada
     */
    int STORE_TYPE_WECHAT_LAZADA = 9;
    /**
     * 店铺类型: 微信小店
     */
    int STORE_TYPE_WECHAT_STORE = 10;

    /**
     * 修改来源: 订单更新
     */
    Integer MODIFY_SOURCE_ORDER = 1;
    /**
     * 修改来源: 自助修改服务
     */
    Integer MODIFY_SOURCE_SELF = 2;

    /**
     * 发票抬头类型: 个人
     */
    Integer INVOICE_TITLE_PERSONAL = 1;
    /**
     * 发票抬头类型: 企业
     */
    Integer INVOICE_TITLE_ENTERPRISE = 2;

    /**
     * 淘宝平台发票抬头类型: 个人
     */
    Integer TB_PLATFORM_BUSINESS_TYPE_PERSONAL = 0;
    /**
     * 淘宝平台发票抬头类型: 企业
     */
    Integer TB_PLATFORM_BUSINESS_TYPE_ENTERPRISE = 1;

    /**
     * 淘宝发票类型: 蓝票
     */
    String INVOICE_TYPE_BLUE = "blue";
    /**
     * 淘宝发票类型: 红票
     */
    String INVOICE_TYPE_RED = "red";

    /**
     * 发票类型: 蓝票
     */
    Integer MATRIX_INVOICE_TYPE_BLUE = 1;
    /**
     * 发票类型: 红票
     */
    Integer MATRIX_INVOICE_TYPE_RED = 2;

    /**
     * 创建方式: 平台获取
     */
    Integer CREATION_METHOD_PLATFORM = 1;
    /**
     * 创建方式: 手工
     */
    Integer CREATION_METHOD_MANUAL = 2;

    /**
     * POS配送方式: 快递
     */
    String POS_DELIVERY_TYPE_EXPRESS = "EXPRESS";
    /**
     * POS配送方式: 自提
     */
    String POS_DELIVERY_TYPE_PICKUP = "PICKUP";

    /**
     * 配送方式: 自提
     */
    Integer DELIVERY_TYPE_PICKUP = 1;
    /**
     * 配送方式: 快递
     */
    Integer DELIVERY_TYPE_EXPRESS = 2;

    /**
     * 订阅状态: 成功
     */
    Integer SUBSCRIBE_STATUS_SUCCESS = 1;
    /**
     * 订阅状态: 失败
     */
    Integer SUBSCRIBE_STATUS_FAIL = 2;

    /**
     * 发货结果单转换方案2
     */
    Integer DELIVERY_SHEET_CONVERSION_SCHEME_2 = 2;

    /**
     * 唯一码管理模式: 无需管理
     */
    Integer UNIQUE_TYPE_NO = 1;

    /**
     * 唯一码管理模式: 管理唯一码
     */
    Integer UNIQUE_TYPE_YES = 2;

    /**
     * 批次管理模式: 无需管理
     */
    Integer BATCH_TYPE_NO = 1;

    /**
     * 批次管理模式: 弱管控
     */
    Integer BATCH_TYPE_LIGHT = 4;

    /**
     * 批次管理模式: 仅管理批次
     */
    Integer BATCH_TYPE_BATCH = 2;
    /**
     * 批次管理模式: 管理批次和效期
     */
    Integer BATCH_TYPE_BATCH_VAL = 3;

    /**
     * 调用矩阵 node_type:
     */
    String MATRIX_NODE_TYPE_QI_MEN = "qimen";

    /**
     * 生效状态: 生效中
     */
    Integer EFFECTIVE_STATUS_EFFECT = 1;
    /**
     * 生效状态: 已失效
     */
    Integer INEFFECTIVE_STATUS_EXPIRED = 2;

    /**
     * 抖音模版类型: 一联单
     */
    String DY_TEMPLATE_TYPE_ONE = "1";
    /**
     * 抖音模版类型: 二联单
     */
    String DY_TEMPLATE_TYPE_TWO = "2";

    /**
     * 模版类型: 一联单
     */
    Integer TEMPLATE_TYPE_ONE = 7;
    /**
     * 模版类型: 二联单
     */
    Integer TEMPLATE_TYPE_TWO = 8;

    /**
     * 业务数据转换履历业务类型: 其他入库单
     */
    Integer BUSINESS_TRANSFORM_HISTORY_BIZ_TYPE_OTHER_WAREHOUSE_GODOWN = 1;

    /**
     * 主数据同步履历业务类型: 后端类目
     */
    Integer BUSINESS_TRANSFORM_HISTORY_BIZ_TYPE_BACKEND_CATEGORY = 1;
    /**
     * 主数据同步履历业务类型: 商品
     */
    Integer BUSINESS_TRANSFORM_HISTORY_BIZ_TYPE_GOODS = 2;

    /**
     * 数据接收结束标志: 接收中
     */
    Integer DATA_RECEIVE_FINISH_FLAG_RECEIVING = 1;
    /**
     * 数据接收结束标志: 接收完毕
     */
    Integer DATA_RECEIVE_FINISH_FLAG_FINISHED = 2;

    /**
     * 百望发票种类: 026：增值税电子发票
     */
    String BW_INVOICE_TYPE_ORDINARY = "026";
    /**
     * 百望发票种类: 028:增值税电子专用发票
     */
    String BW_INVOICE_TYPE_SPECIAL = "028";
    /**
     * 百望发票种类: 01:全电发票(增值税专用发票)
     */
    String BW_INVOICE_TYPE_ELECTRON_SPECIAL = "01";
    /**
     * 百望发票种类: 02:全电发票(普通发票)
     */
    String BW_INVOICE_TYPE_ELECTRON_ORDINARY = "02";

    /**
     * 发票类型: 1:增值税普通发票
     */
    Integer INVOICING_TYPE_ORDINARY = 1;
    /**
     * 发票类型: 2:增值税专用发票
     */
    Integer INVOICING_TYPE_SPECIAL = 2;
    /**
     * 发票类型: 3:全电发票(增值税普通发票)
     */
    Integer INVOICING_TYPE_ELECTRON_ORDINARY = 3;
    /**
     * 发票类型: 4:全电发票(增值税专用发票)
     */
    Integer INVOICING_TYPE_ELECTRON_SPECIAL = 4;

    /**
     * 百望录入方身份:  01:销方
     */
    String BW_ENTRY_IDENTITY_SELLER = "01";
    /**
     * 百望录入方身份: 02:购方
     */
    String BW_ENTRY_IDENTITY_BUYER = "02";

    /**
     * 百望是否纸质发票标志: Y：纸质发票
     */
    String BW_IS_PAPER_YES = "Y";
    /**
     * 百望是否纸质发票标志: N：电子发票
     */
    String BW_IS_PAPER_NO = "N";

    /**
     * 百望发票来源: 1：:增值税发票管理系统
     */
    String BW_INVOICE_SOURCE_SYSTEM = "1";
    /**
     * 百望发票来源: 2：:电子发票服务平台
     */
    String BW_INVOICE_SOURCE_PLATFORM = "2";

    /**
     * 百望返回类型: 1：PDF
     */
    String BW_RETURN_TYPE_PDF = "1";
    /**
     * 百望返回类型: 2：文件流
     */
    String BW_RETURN_TYPE_IO = "2";

    /**
     * 百望by1: PDF
     */
    String BW_BY1_PDF = "PDF";
    /**
     * 百望by1: OFD
     */
    String BW_BY1_OFD = "OFD";
    /*
     * 接口描述: 数据推送
     */
    Integer INTERFACE_DESCRIPTION_DATA_PUSH = 1;
    /**
     * 接口描述: 数据调用
     */
    Integer INTERFACE_DESCRIPTION_DATA_INVOKE = 2;
    /**
     * 接口描述: 数据接收
     */
    Integer INTERFACE_DESCRIPTION_DATA_RECEIVE = 3;

    /**
     * 执行结果: 成功
     */
    Integer API_RESULT_SUCCESS = 1;
    /**
     * 执行结果: 失败
     */
    Integer API_RESULT_FAILURE = 2;
    /**
     * 发票结果 转换状态 ：成功
     */
    Integer INVOICE_CONVERT_STATUS_SUCCESS = 1;
    /**
     * 发票结果 开具状态 ：成功
     */
    String INVOICE_RESULT_APPLY_STATUS_SUCCESS = "00";
    /**
     * 发票结果 开具状态 ：失败
     */
    String INVOICE_RESULT_APPLY_STATUS_FAIL = "00";
    /**
     * 发票结果 开具类型 ：蓝票
     */
    String INVOICE_RESULT_TYPE = "0";
    /**
     * 增值税电子普通发票
     */
    Integer INVOICING_TYPE_ELECTRONIC_ORDINARY = 0;
    /**
     * 增值税纸质普通发票
     */
    Integer INVOICING_TYPE_PAPER_ORDINARY = 1;
    /**
     * 增值税专用发票
     */
    Integer INVOICING_TYPE_DEDICATED = 2;
    /**
     * 增值税电子专用发票
     */
    Integer INVOICING_TYPE_ELECTRONIC_DEDICATED = 3;
    /**
     * 全电发票（增值税普通发票）
     */
    Integer INVOICING_TYPE_E_ORDINARY = 4;
    /**
     * 全电发票（增值税专用发票）
     */
    Integer INVOICING_TYPE_E_DEDICATED = 5;

    String BW_INVOICE_CONVERSION_ALREADY = "无需转换";
    String BW_INVOICE_ROW_TYPE_DISCOUNT = "1";
    /**
     * 业务节点 处理完成
     */
    Integer BUSINESS_NODE_FINISH = 1;

    /**
     * 售后类型: 已发货仅退款
     */
    Integer AFTER_SALE_TYPE_ONLY = 1;

    /**
     * 审核状态：未审核
     */
    Integer NOT_APPROVED = 0;
    /**
     * 审核状态：已审核
     */
    Integer ALREADY_APPROVED = 1;

    /**
     * 得物订单业务类型
     * 0:普通现货订单，1：极速现货订单，2：品牌直发订单
     */
    Integer ORDER_BUSINESS_TYPE_0 = 0;
    Integer ORDER_BUSINESS_TYPE_1 = 1;
    Integer ORDER_BUSINESS_TYPE_2 = 2;
}
