package cn.shopex.hobbits.ms;

import cn.hutool.json.JSONUtil;
import cn.shopex.hobbits.ms.harbor.api.inner.biz.ApiMatrixBiz;
import cn.shopex.hobbits.ms.harbor.api.inner.biz.PlatformGoodsDownloadBiz;
import cn.shopex.hobbits.ms.harbor.api.inner.domain.DewuSellerAddressResp;
import cn.shopex.hobbits.ms.harbor.api.inner.domain.OnlinePlatformGoodsDto;
import cn.shopex.hobbits.ms.harbor.api.inner.domain.OrderDecryptReq;
import cn.shopex.hobbits.ms.harbor.api.inner.domain.OrderDecryptRes;
import cn.shopex.hobbits.ms.harbor.callback.order.biz.MatrixCallbackBiz;
import cn.shopex.hobbits.ms.harbor.common.CommonConst;
import cn.shopex.hobbits.ms.harbor.common.order.constant.OriginalOrderFieldConst;
import cn.shopex.hobbits.ms.harbor.web.order.original.biz.OriginalOrderBiz;
import cn.shopex.hobbits.ms.harbor.web.order.original.service.OriginalOrderCommonService;
import cn.shopex.hobbits.ms.library.redundant.configuration.domain.RedundantShopexAuthorization;
import cn.shopex.hobbits.ms.library.redundant.configuration.service.ApplicationRedundantService;
import cn.shopex.hobbits.neb.MatrixClient;
import cn.shopex.hobbits.neb.domain.DewuQuerySellerAddressParam;
import cn.shopex.hobbits.neb.domain.KsStoreItemSkuListParam;
import cn.shopex.hobbits.neb.domain.MatrixResponse;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;
import java.util.List;

import static cn.shopex.hobbits.ms.library.utils.DateUtil.*;

/**
 * <AUTHOR>
 * @date 2025/6/18 14:02
 * @description
 */
@Slf4j
@SpringBootTest
public class KsOrderTest {
    @Autowired
    private MatrixCallbackBiz matrixCallbackBiz;
    @Autowired
    private OriginalOrderCommonService originalOrderCommonService;
    @Autowired
    private PlatformGoodsDownloadBiz platformGoodsDownloadBiz;
    @Autowired
    private OriginalOrderBiz originalOrderBiz;
    @Autowired
    private ApiMatrixBiz apiMatrixBiz;
    @Autowired
    private ApiMatrixBiz apiMatrixBiz;
    @Autowired
    private ApplicationRedundantService applicationRedundantService;
    private final static String str = "{\"app_id\":\"ecos.ome\",\"method\":\"ome.order.add\",\"from_node_id\":\"1265192532\",\"trade_from\":\"NORMAL\",\"to_node_id\":\"1286120139\",\"order_source\":\"dewu\",\"date\":\"2025-05-20 11:47:12\",\"t_type\":\"fixed\",\"order_bn\":\"110177340669519553\",\"title\":\"得物订单\",\"is_risk\":\"\",\"cost_item\":\"599.0\",\"total_amount\":\"599.0\",\"payed\":\"599.0\",\"pmt_order\":\"0.0\",\"mark_text\":\"\",\"custom_mark\":\"\",\"mark_type\":\"\",\"payments\":\"[{\\\"paymethod\\\": \\\"\\\\u5728\\\\u7ebf\\\\u652f\\\\u4ed8\\\",  \\\"pay_time\\\": \\\"2025-05-19 20:48:57\\\", \\\"trade_no\\\": \\\"110177340669519553\\\",  \\\"pay_bn\\\": \\\"110177340669519553\\\", \\\"money\\\": 599.0}]\",\"step_trade_status\":\"\",\"step_paid_fee\":\"\",\"trade_type\":\"\",\"createtime\":\"1747658935\",\"modified\":\"1747710443\",\"lastmodify\":\"1747710443\",\"pmt_detail\":\"[]\",\"shipping\":\"{\\\"shipping_name\\\": \\\"SF\\\", \\\"is_cod\\\": \\\"false\\\", \\\"is_virtual_delivery\\\": \\\"false\\\", \\\"is_protect\\\": \\\"\\\", \\\"cost_shipping\\\": 0.0, \\\"cost_protect\\\": \\\"\\\", \\\"shipping_id\\\": \\\"\\\"}\",\"is_yushou\":\"false\",\"consignee\":\"{\\\"area_street\\\": null, \\\"telephone\\\": \\\"\\\", \\\"area_state\\\": \\\"\\\\u8d35\\\\u5dde\\\\u7701\\\", \\\"lat\\\": \\\"\\\", \\\"addr\\\": \\\"\\\\u8d35\\\\u5dde\\\\u7701\\\\u94dc\\\\u4ec1\\\\u5e02\\\\u78a7\\\\u6c5f\\\\u533a\\\\u6cb3\\\\u897f\\\\u8857\\\\u9053*****\\\", \\\"r_time\\\": \\\"\\\", \\\"name\\\": \\\"*\\\", \\\"zip\\\": \\\"\\\", \\\"mobile\\\": \\\"173****0623\\\", \\\"country\\\": \\\"\\\", \\\"lon\\\": \\\"\\\", \\\"area_city\\\": \\\"\\\\u94dc\\\\u4ec1\\\\u5e02\\\", \\\"area_district\\\": \\\"\\\\u78a7\\\\u6c5f\\\\u533a\\\", \\\"email\\\": \\\"\\\"}\",\"source_status\":\"WAIT_SELLER_SEND_GOODS\",\"order_objects\":\"[{\\\"name\\\":\\\"测试\\\",\\\"pmt_price\\\":0.0,\\\"bn\\\":\\\"ES-CM31-H405\\\",\\\"oid\\\":\\\"863032541\\\",\\\"order_items\\\":[{\\\"pmt_price\\\":0.0}],\\\"amount\\\":599.0,\\\"shop_goods_id\\\":\\\"\\\",\\\"shop_product_id\\\":863032541,\\\"part_mjz_discount\\\":0.0,\\\"divide_order_fee\\\":599.0,\\\"price\\\":599.0,\\\"estimate_con_time\\\":\\\"\\\",\\\"part_mjz_discount\\\":0.0,\\\"adjust_fee\\\":\\\"\\\",\\\"quantity\\\":1}]\",\"index_field\":\"{\\\"open_address_id\\\": \\\"3dcsdvdsfvssdcscsdvdf\\\"}\",\"extend_field\":\"{\\\"performance_type\\\": \\\"1\\\",\\\"order_type\\\": \\\"26\\\"}\"}";
    @Test
    public void matrix() {
        JSONObject jsonObject = JSONObject.parseObject(str);
        matrixCallbackBiz.matrix(jsonObject);
    }

    @Test
    public void originalOrderDown() {
        LocalDateTime now = LocalDateTime.now();
        Long operateTimeUxt = formatUnixTime(now);
        JSONObject jsonObject = JSONObject.parseObject(str);
        jsonObject.put(OriginalOrderFieldConst.EXTRA_APPEND_FIELD_STORE_CODE, "DEWU");
        jsonObject.put(OriginalOrderFieldConst.EXTRA_APPEND_FIELD_SOURCE_PLATFORM, 12);
        jsonObject.put(OriginalOrderFieldConst.EXTRA_APPEND_FIELD_ORDER_DOWN_TIME, operateTimeUxt);
        jsonObject.put(OriginalOrderFieldConst.EXTRA_APPEND_FIELD_BUSINESS_TYPE, "");
        originalOrderCommonService.saveOriginalOrder(jsonObject);
    }


    @Test
    public void ksGoodsDown() {
        List<OnlinePlatformGoodsDto> platformGoodsItemList = platformGoodsDownloadBiz.getPlatformGoodsItemList("1299140733", 11, null, null);
        log.info("ksGoodsDown:{}", platformGoodsItemList);
    }

    @Test
    public void orderDecrypt() {
        OrderDecryptReq orderDecryptReq = new OrderDecryptReq();
        orderDecryptReq.setOnlineStoreCode("KS");
        orderDecryptReq.setOrderBn("2521102241224638");
        orderDecryptReq.setOrderType(CommonConst.RECEIPT_TYPE_ORDER_CHANGE);
        OrderDecryptRes orderDecryptRes = apiMatrixBiz.orderDecrypt(orderDecryptReq);
        log.info("orderDecryptRes:{}", orderDecryptRes);
    }

    @Test
    public void queryDewuSellerAddress() throws JsonProcessingException {
        List<OnlinePlatformGoodsDto> platformGoodsItemList = platformGoodsDownloadBiz.getPlatformGoodsItemList("1295160431", 12, "2025-07-01 00:00:00", "2025-07-22 00:00:00");
        log.info("queryDewuSellerAddress:{}", platformGoodsItemList);
    }
}
