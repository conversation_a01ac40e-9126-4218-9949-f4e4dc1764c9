package cn.shopex.hobbits.ms.harbor.web.order.decryption.service.impl;

import cn.shopex.hobbits.ms.harbor.common.CommonConst;
import cn.shopex.hobbits.ms.harbor.common.order.conversion.OrderConversionCommon;
import cn.shopex.hobbits.ms.harbor.common.order.conversion.OrderDecryptConversionHandle;
import cn.shopex.hobbits.ms.harbor.web.order.decryption.service.OrderDecryptionService;
import cn.shopex.hobbits.ms.harbor.web.order.decryption.mapper.OrderDecryptionBizMapper;
import cn.shopex.hobbits.ms.library.encrypt.ReversibleEncrypt;
import cn.shopex.hobbits.ms.orm.mapper.*;
import cn.shopex.hobbits.ms.orm.model.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static cn.shopex.hobbits.ms.harbor.common.order.constant.OriginalOrderFieldConst.*;

/**
 * 订单解密service
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class OrderDecryptionServiceImpl implements OrderDecryptionService {

    private final OrderDecryptionDouyinMapper orderDecryptionDouyinMapper;
    private final OrderDecryptionTaobaoMapper orderDecryptionTaobaoMapper;
    private final OrderDecryptionJingdongMapper orderDecryptionJDMapper;
    private final OrderDecryptionPinduoduoMapper orderDecryptionPinduoduoMapper;
    private final OrderDecryptionBizMapper orderDecryptionBizMapper;
    private final OrderDecryptionPrivateMapper orderDecryptionPrivateMapper;
    private final OrderDecryptionXiaohongshuMapper orderDecryptionXiaohongshuMapper;
    private final OrderDecryptionWeipinhuiMapper orderDecryptionWeipinhuiMapper;
    private final OrderDecryptionMomoMapper orderDecryptionMomoMapper;
    private final OrderDecryptionWechatStoreMapper orderDecryptionWechatStoreMapper;
    private final OrderDecryptionKuaishouMapper orderDecryptionKuaishouMapper;
    private final OrderDecryptionDewuMapper orderDecryptionDewuMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrderDecryptionInfo(Integer onlineStoreType, String receiptNumber, Integer receiptType, Map<String, Object> orderInfo, String operator, String operatorTime, Long operatorTimeUxt) throws JsonProcessingException {
        if (Objects.equals(onlineStoreType, CommonConst.SOURCE_PLATFORM_TB)) {
            HashMap<String, Object> mapInfo;
            if (Objects.equals(receiptType, CommonConst.RECEIPT_TYPE_ORDER)) {
                // 平台订单
                mapInfo = OrderConversionCommon.getMapObject(orderInfo, OrderDecryptConversionHandle.ORIGINAL_ORDER_J_Z_FIELD_EXTEND_FIELD);
            } else {
                // 平台换货单
                mapInfo = OrderConversionCommon.getMapObject(orderInfo, OrderDecryptConversionHandle.ORIGINAL_ORDER_J_Z_FIELD_INDEX_FIELD);
            }
            String oaid = OrderDecryptConversionHandle.getOaid(onlineStoreType, mapInfo);
            if (StringUtils.isBlank(oaid)) {
                // 如果上面方式没有拿到oaid 尝试从最外层取
                oaid = MapUtils.getString(orderInfo, OrderDecryptConversionHandle.TB_ORIGINAL_ORDER_J_Z_FIELD_EXTEND_FIELD_OAID, StringUtils.EMPTY);
            }
            OrderDecryptionTaobao orderDecryptionTaobao = this.orderDecryptionBizMapper.selectOrderDecryptionTaobaoByReceiptNumberAndReceiptType(receiptNumber, receiptType);
            if (Objects.nonNull(orderDecryptionTaobao)) {
                orderDecryptionTaobao.setOaid(oaid);
                orderDecryptionTaobao.setUpdateUser(operator);
                orderDecryptionTaobao.setUpdateTimeUxt(operatorTimeUxt);
                orderDecryptionTaobao.setUpdateTime(operatorTime);
                this.orderDecryptionTaobaoMapper.update(orderDecryptionTaobao);
                return;
            }
            orderDecryptionTaobao = OrderDecryptConversionHandle.createOrderDecryptionTaobao(receiptNumber, receiptType, oaid, operator, operatorTime, operatorTimeUxt);
            this.orderDecryptionTaobaoMapper.create(orderDecryptionTaobao);
        }else if (Objects.equals(onlineStoreType, CommonConst.SOURCE_PLATFORM_JD)) {
            HashMap<String, Object> mapInfo;
            if (Objects.equals(receiptType, CommonConst.RECEIPT_TYPE_ORDER)) {
                // 平台订单
                mapInfo = OrderConversionCommon.getMapObject(orderInfo, OrderDecryptConversionHandle.ORIGINAL_ORDER_J_Z_FIELD_EXTEND_FIELD);
            } else {
                // 平台换货单
                mapInfo = OrderConversionCommon.getMapObject(orderInfo, OrderDecryptConversionHandle.ORIGINAL_ORDER_J_Z_FIELD_INDEX_FIELD);
            }
            String oaid = OrderDecryptConversionHandle.getOaid(onlineStoreType, mapInfo);
            if (StringUtils.isBlank(oaid)) {
                // 如果上面方式没有拿到oaid 尝试从最外层取
                oaid = MapUtils.getString(orderInfo, OrderDecryptConversionHandle.TB_ORIGINAL_ORDER_J_Z_FIELD_EXTEND_FIELD_OAID, StringUtils.EMPTY);
            }
            OrderDecryptionJD orderDecryptionJD = this.orderDecryptionBizMapper.selectOrderDecryptionJDByReceiptNumberAndReceiptType(receiptNumber, receiptType);
            if (Objects.nonNull(orderDecryptionJD)) {
                orderDecryptionJD.setOaid(oaid);
                orderDecryptionJD.setUpdateUser(operator);
                orderDecryptionJD.setUpdateTimeUxt(operatorTimeUxt);
                orderDecryptionJD.setUpdateTime(operatorTime);
                this.orderDecryptionJDMapper.update(orderDecryptionJD);
                return;
            }
            orderDecryptionJD = OrderDecryptConversionHandle.createOrderDecryptionJD(receiptNumber, receiptType, oaid, operator, operatorTime, operatorTimeUxt);
            this.orderDecryptionJDMapper.create(orderDecryptionJD);
        } else if (Objects.equals(onlineStoreType, CommonConst.SOURCE_PLATFORM_DY)) {
            HashMap<String, Object> mapInfo = OrderConversionCommon.getMapObject(orderInfo, OrderDecryptConversionHandle.ORIGINAL_ORDER_J_Z_FIELD_INDEX_FIELD);
            OrderDecryptionDouyin orderDecryptionDouyin = this.orderDecryptionBizMapper.selectOrderDecryptionDouyinByReceiptNumberAndReceiptType(receiptNumber, receiptType);
            if (Objects.nonNull(orderDecryptionDouyin)) {
                OrderDecryptConversionHandle.paddingDouyinDecryptionInfo(orderDecryptionDouyin, mapInfo);
                orderDecryptionDouyin.setUpdateUser(operator);
                orderDecryptionDouyin.setUpdateTimeUxt(operatorTimeUxt);
                orderDecryptionDouyin.setUpdateTime(operatorTime);
                this.orderDecryptionDouyinMapper.update(orderDecryptionDouyin);
                return;
            }
            orderDecryptionDouyin = OrderDecryptConversionHandle.createOrderDecryptionDouyin(receiptNumber, receiptType, mapInfo, operator, operatorTime, operatorTimeUxt);
            this.orderDecryptionDouyinMapper.create(orderDecryptionDouyin);
        } else if (Objects.equals(onlineStoreType, CommonConst.SOURCE_PLATFORM_PDD)) {
            HashMap<String, Object> mapInfo = OrderConversionCommon.getMapObject(orderInfo, OrderDecryptConversionHandle.ORIGINAL_ORDER_J_Z_FIELD_INDEX_FIELD);
            OrderDecryptionPinduoduo orderDecryptionPinduoduo = this.orderDecryptionBizMapper.selectOrderDecryptionPinduoduoByReceiptNumberAndReceiptType(receiptNumber, receiptType);
            if (Objects.nonNull(orderDecryptionPinduoduo)) {
                OrderDecryptConversionHandle.paddingPinduoduoDecryptionInfo(orderDecryptionPinduoduo, mapInfo);
                orderDecryptionPinduoduo.setUpdateUser(operator);
                orderDecryptionPinduoduo.setUpdateTimeUxt(operatorTimeUxt);
                orderDecryptionPinduoduo.setUpdateTime(operatorTime);
                this.orderDecryptionPinduoduoMapper.update(orderDecryptionPinduoduo);
                return;
            }
            orderDecryptionPinduoduo = OrderDecryptConversionHandle.createOrderDecryptionPinduoduo(receiptNumber, receiptType, mapInfo, operator, operatorTime, operatorTimeUxt);
            this.orderDecryptionPinduoduoMapper.create(orderDecryptionPinduoduo);
        } else if (Objects.equals(onlineStoreType, CommonConst.SOURCE_PLATFORM_XHS)) {
            // 保存小红书订单加密信息
            HashMap<String, Object> mapInfo = OrderConversionCommon.getMapObject(orderInfo,
                    OrderDecryptConversionHandle.ORIGINAL_ORDER_J_Z_FIELD_INDEX_FIELD);
            OrderDecryptionXiaohongshu orderDecryptionXiaohongshu = this.orderDecryptionBizMapper
                    .selectOrderDecryptionXiaohongshuByReceiptNumberAndReceiptType(receiptNumber, receiptType);
            if (Objects.nonNull(orderDecryptionXiaohongshu)) {
                OrderDecryptConversionHandle.paddingXiaohongshuDecryptionInfo(orderDecryptionXiaohongshu, mapInfo);
                //判断如果地址不为空，才更新地址
                if(StringUtils.isNotEmpty(orderDecryptionXiaohongshu.getReceiverAddress())){
                    orderDecryptionXiaohongshu.setUpdateUser(operator);
                    orderDecryptionXiaohongshu.setUpdateTimeUxt(operatorTimeUxt);
                    orderDecryptionXiaohongshu.setUpdateTime(operatorTime);
                    this.orderDecryptionXiaohongshuMapper.update(orderDecryptionXiaohongshu);
                }
                return;
            }
            orderDecryptionXiaohongshu = OrderDecryptConversionHandle.createOrderDecryptionXiaohongshu(receiptNumber,
                    receiptType, mapInfo, operator, operatorTime, operatorTimeUxt);
            this.orderDecryptionXiaohongshuMapper.create(orderDecryptionXiaohongshu);
        } else if (Objects.equals(onlineStoreType, CommonConst.SOURCE_PLATFORM_WPH)) {
            // 保存唯品会订单加密信息
            // 收货人信息使用自有加密方式进行加密，保存到接口中的加解密表
            HashMap<String, Object> consignee = OrderConversionCommon.getMapObject(orderInfo,
                    ORIGINAL_ORDER_J_Z_FIELD_CONSIGNEE);
            String name = MapUtils.getString(consignee, ORIGINAL_ORDER_J_Z_FIELD_CONSIGNEE_CONSIGNEE_NAME);
            String mobile = MapUtils.getString(consignee, ORIGINAL_ORDER_J_Z_FIELD_CONSIGNEE_CONSIGNEE_MOBILE);
            String address = MapUtils.getString(consignee, ORIGINAL_ORDER_J_Z_FIELD_CONSIGNEE_ADDR);
            String tel = MapUtils.getString(consignee, ORIGINAL_ORDER_J_Z_FIELD_CONSIGNEE_CONSIGNEE_TELEPHONE);
            String oaid = ReversibleEncrypt.getOaid(mobile, tel, name, address);
            OrderDecryptionWeipinhui orderDecryptionWeipinhui = this.orderDecryptionBizMapper
                    .selectOrderDecryptionWeipinhuiByReceiptNumberAndReceiptType(receiptNumber, receiptType);
            if (Objects.nonNull(orderDecryptionWeipinhui)) {
                orderDecryptionWeipinhui.setOaid(oaid);
                orderDecryptionWeipinhui.setUpdateUser(operator);
                orderDecryptionWeipinhui.setUpdateTimeUxt(operatorTimeUxt);
                orderDecryptionWeipinhui.setUpdateTime(operatorTime);
                this.orderDecryptionWeipinhuiMapper.update(orderDecryptionWeipinhui);
                return;
            }
            orderDecryptionWeipinhui = OrderDecryptConversionHandle.createOrderDecryptionWeipinhui(receiptNumber,
                    receiptType, oaid, operator, operatorTime, operatorTimeUxt);
            this.orderDecryptionWeipinhuiMapper.create(orderDecryptionWeipinhui);
        } else if (Objects.equals(onlineStoreType, CommonConst.SOURCE_PLATFORM_WECHAT_STORE)) {
            // 保存微信小店订单加密信息
            HashMap<String, Object> mapInfo = OrderConversionCommon.getMapObject(orderInfo, OrderDecryptConversionHandle.ORIGINAL_ORDER_J_Z_FIELD_INDEX_FIELD);
            String oaid = OrderDecryptConversionHandle.getOaid(onlineStoreType, mapInfo);
            OrderDecryptionWechatStore orderDecryptionWechatStore = this.orderDecryptionBizMapper.selectOrderDecryptionWechatStoreByReceiptNumberAndReceiptType(receiptNumber, receiptType);
            if (Objects.nonNull(orderDecryptionWechatStore)) {
                orderDecryptionWechatStore.setOaid(oaid);
                orderDecryptionWechatStore.setUpdateUser(operator);
                orderDecryptionWechatStore.setUpdateTimeUxt(operatorTimeUxt);
                orderDecryptionWechatStore.setUpdateTime(operatorTime);
                this.orderDecryptionWechatStoreMapper.update(orderDecryptionWechatStore);
                return;
            }
            orderDecryptionWechatStore = OrderDecryptConversionHandle.createOrderDecryptionWechatStore(receiptNumber, receiptType, oaid, operator, operatorTime, operatorTimeUxt);
            this.orderDecryptionWechatStoreMapper.create(orderDecryptionWechatStore);
        } else if (Objects.equals(onlineStoreType, CommonConst.SOURCE_PLATFORM_KS)) {
            // 保存快手订单加密信息
            // 收货人信息使用自有加密方式进行加密，保存到接口中的加解密表
            HashMap<String, Object> mapInfo = OrderConversionCommon.getMapObject(orderInfo, OrderDecryptConversionHandle.ORIGINAL_ORDER_J_Z_FIELD_INDEX_FIELD);
            OrderDecryptionKs orderDecryptionKs = this.orderDecryptionBizMapper.selectOrderDecryptionKuaishouByReceiptNumberAndReceiptType(receiptNumber, receiptType);
            if (Objects.nonNull(orderDecryptionKs)) {
                OrderDecryptConversionHandle.paddingKuaishouDecryptionInfo(orderDecryptionKs, mapInfo);
                orderDecryptionKs.setUpdateUser(operator);
                orderDecryptionKs.setUpdateTimeUxt(operatorTimeUxt);
                orderDecryptionKs.setUpdateTime(operatorTime);
                this.orderDecryptionKuaishouMapper.update(orderDecryptionKs);
                return;
            }
            orderDecryptionKs = OrderDecryptConversionHandle.createOrderDecryptionKuaishou(receiptNumber, receiptType, mapInfo, operator, operatorTime, operatorTimeUxt);
            this.orderDecryptionKuaishouMapper.create(orderDecryptionKs);
        } else if (Objects.equals(onlineStoreType, CommonConst.SOURCE_PLATFORM_DEWU)) {
            // 保存得物订单加密信息
            HashMap<String, Object> mapInfo = OrderConversionCommon.getMapObject(orderInfo, OrderDecryptConversionHandle.ORIGINAL_ORDER_J_Z_FIELD_INDEX_FIELD);
            String oaid = OrderDecryptConversionHandle.getOaid(onlineStoreType, mapInfo);
            OrderDecryptionDewu orderDecryptionDewu = this.orderDecryptionBizMapper.selectOrderDecryptionDewuByReceiptNumberAndReceiptType(receiptNumber, receiptType);
            if (Objects.nonNull(orderDecryptionDewu)) {
                orderDecryptionDewu.setOaid(oaid);
                orderDecryptionDewu.setUpdateUser(operator);
                orderDecryptionDewu.setUpdateTimeUxt(operatorTimeUxt);
                orderDecryptionDewu.setUpdateTime(operatorTime);
                this.orderDecryptionDewuMapper.update(orderDecryptionDewu);
                return;
            }
            orderDecryptionDewu = OrderDecryptConversionHandle.createOrderDecryptionDewu(receiptNumber, receiptType, oaid, operator, operatorTime, operatorTimeUxt);
            this.orderDecryptionDewuMapper.create(orderDecryptionDewu);
        }

        // 私域
        if (onlineStoreType >= CommonConst.SOURCE_PLATFORM_EZR) {
            HashMap<String, Object> mapInfo = OrderConversionCommon.getMapObject(orderInfo, OrderDecryptConversionHandle.ORIGINAL_ORDER_J_Z_FIELD_INDEX_FIELD);
            String oaid = OrderDecryptConversionHandle.getOaid(onlineStoreType, mapInfo);
            OrderDecryptionPrivate orderDecryptionPrivate = this.orderDecryptionBizMapper.selectOrderDecryptionPrivateByReceiptNumberAndReceiptType(receiptNumber, receiptType);
            if (Objects.nonNull(orderDecryptionPrivate)) {
                orderDecryptionPrivate.setOaid(oaid);
                orderDecryptionPrivate.setUpdateUser(operator);
                orderDecryptionPrivate.setUpdateTimeUxt(operatorTimeUxt);
                orderDecryptionPrivate.setUpdateTime(operatorTime);
                this.orderDecryptionPrivateMapper.update(orderDecryptionPrivate);
                return;
            }
            orderDecryptionPrivate = OrderDecryptConversionHandle.createOrderDecryptionPrivate(receiptNumber, receiptType, oaid, operator, operatorTime, operatorTimeUxt);
            this.orderDecryptionPrivateMapper.create(orderDecryptionPrivate);
        }

        // momo
        if (onlineStoreType >= CommonConst.SOURCE_PLATFORM_MOMO) {
            HashMap<String, Object> mapInfo = OrderConversionCommon.getMapObject(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_CONSIGNEE);
            String oaid = OrderDecryptConversionHandle.getOaid(onlineStoreType, mapInfo);
            OrderDecryptionMomo orderDecryptionMomo = this.orderDecryptionBizMapper.selectOrderDecryptionMomoByReceiptNumberAndReceiptType(receiptNumber, receiptType);
            if (Objects.nonNull(orderDecryptionMomo)) {
                orderDecryptionMomo.setOaid(oaid);
                orderDecryptionMomo.setUpdateUser(operator);
                orderDecryptionMomo.setUpdateTimeUxt(operatorTimeUxt);
                orderDecryptionMomo.setUpdateTime(operatorTime);
                this.orderDecryptionMomoMapper.update(orderDecryptionMomo);
                return;
            }
            orderDecryptionMomo = OrderDecryptConversionHandle.createOrderDecryptionMomo(receiptNumber, receiptType, oaid, operator, operatorTime, operatorTimeUxt);
            this.orderDecryptionMomoMapper.create(orderDecryptionMomo);
        }
    }

    @Override
    public OrderDecryptionTaobao selectOrderDecryptionTaobaoByReceiptNumberAndReceiptType(String receiptNumber, Integer receiptType) {
        return this.orderDecryptionBizMapper.selectOrderDecryptionTaobaoByReceiptNumberAndReceiptType(receiptNumber, receiptType);
    }

    @Override
    public OrderDecryptionJD selectOrderDecryptionJDByReceiptNumberAndReceiptType(String receiptNumber, Integer receiptType) {
        return this.orderDecryptionBizMapper.selectOrderDecryptionJDByReceiptNumberAndReceiptType(receiptNumber, receiptType);
    }

    @Override
    public OrderDecryptionDouyin selectOrderDecryptionDouyinByReceiptNumberAndReceiptType(String receiptNumber, Integer receiptType) {
        return this.orderDecryptionBizMapper.selectOrderDecryptionDouyinByReceiptNumberAndReceiptType(receiptNumber, receiptType);
    }

    @Override
    public OrderDecryptionPinduoduo selectOrderDecryptionPinduoduoByReceiptNumberAndReceiptType(String receiptNumber, Integer receiptType) {
        return this.orderDecryptionBizMapper.selectOrderDecryptionPinduoduoByReceiptNumberAndReceiptType(receiptNumber, receiptType);
    }

    @Override
    public OrderDecryptionXiaohongshu selectOrderDecryptionXiaohongshuByReceiptNumberAndReceiptType(
            String receiptNumber, Integer receiptType) {
        return this.orderDecryptionBizMapper.selectOrderDecryptionXiaohongshuByReceiptNumberAndReceiptType(
                receiptNumber, receiptType);
    }

    @Override
    public OrderDecryptionWeipinhui selectOrderDecryptionWeipinhuiByReceiptNumberAndReceiptType(
            String receiptNumber, Integer receiptType) {
        return this.orderDecryptionBizMapper.selectOrderDecryptionWeipinhuiByReceiptNumberAndReceiptType(
                receiptNumber, receiptType);
    }

    @Override
    public OrderDecryptionPrivate selectOrderDecryptionPrivateByReceiptNumberAndReceiptType(String receiptNumber, Integer receiptType) {
        return this.orderDecryptionBizMapper.selectOrderDecryptionPrivateByReceiptNumberAndReceiptType(receiptNumber, receiptType);
    }

    @Override
    public OrderDecryptionMomo selectOrderDecryptionMomoByReceiptNumberAndReceiptType(String receiptNumber, Integer receiptType) {
        return this.orderDecryptionBizMapper.selectOrderDecryptionMomoByReceiptNumberAndReceiptType(receiptNumber, receiptType);
    }

    @Override
    public OrderDecryptionJD selectOrderDecryptionJdByReceiptNumberAndReceiptType(String receiptNumber, Integer receiptType) {
        return this.orderDecryptionBizMapper.selectOrderDecryptionJdByReceiptNumberAndReceiptType(receiptNumber, receiptType);
    }

    @Override
    public OrderDecryptionKs selectOrderDecryptionKsByReceiptNumberAndReceiptType(String receiptNumber, Integer receiptType) {
        return this.orderDecryptionBizMapper.selectOrderDecryptionKuaishouByReceiptNumberAndReceiptType(receiptNumber, receiptType);
    }

    @Override
    public OrderDecryptionDewu selectOrderDecryptionDewuByReceiptNumberAndReceiptType(String receiptNumber, Integer receiptType) {
        return this.orderDecryptionBizMapper.selectOrderDecryptionDewuByReceiptNumberAndReceiptType(receiptNumber, receiptType);
    }

    @Override
    public OrderDecryptionWechatStore selectOrderDecryptionWechatStoreByReceiptNumberAndReceiptType(String receiptNumber, Integer receiptType) {
        return orderDecryptionBizMapper.selectOrderDecryptionWechatStoreByReceiptNumberAndReceiptType(receiptNumber, receiptType);
    }
}
