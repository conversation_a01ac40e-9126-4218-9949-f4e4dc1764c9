package cn.shopex.hobbits.ms.harbor.common.order.conversion;

import cn.shopex.hobbits.ms.harbor.common.CommonConst;
import cn.shopex.hobbits.ms.harbor.common.PropertiesConst;
import cn.shopex.hobbits.ms.harbor.common.domain.order.*;
import cn.shopex.hobbits.ms.harbor.common.order.constant.OriginalOrderFieldConst;
import cn.shopex.hobbits.ms.library.utils.DateUtil;
import cn.shopex.hobbits.ms.library.utils.DesensitizationUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static cn.shopex.hobbits.ms.harbor.common.order.constant.OriginalOrderFieldConst.*;
import static cn.shopex.hobbits.ms.harbor.common.order.conversion.OriginalOrderConversion.OriginalOrderConversionConst.*;
import static cn.shopex.hobbits.ms.library.utils.DateUtil.FMT_sdf14_L;
import static cn.shopex.hobbits.ms.library.utils.DateUtil.paresDateStrToLongUnix;

/**
 * <AUTHOR>
 */
@Slf4j
public class OriginalOrderConversion extends OrderConversionCommon {

    /**
     * 获取平台最后更新时间
     *
     * @param orderInfo 原始订单信息
     * @return 平台最后更新时间
     */
    public static Long getLastModifiedTime(Map<String, Object> orderInfo) {
        return getMapLong(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_MODIFIED) * 1000;
    }

    /**
     * 获取平台订单号
     *
     * @param orderInfo 原始订单信息
     * @return 平台订单号
     */
    public static String getOrderBn(Map<String, Object> orderInfo) {
        return MapUtils.getString(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_TID);
    }

    /**
     * 获取平台子订单信息
     *
     * @param orderInfo 原始订单信息
     * @return 平台子订单信息
     */
    public static String getOrderObjects(Map<String, Object> orderInfo) {
        return MapUtils.getString(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_ORDER_OBJECTS);
    }

    /**
     * 创建RetailOrderDto
     *
     * @param orderInfo 原始订单信息
     * @return RetailOrderDto
     */
    public static RetailOrderDto createRetailOrderDto(Map<String, Object> orderInfo) throws JsonProcessingException {
        RetailOrderDto retailOrderDto = new RetailOrderDto();
        // 店铺类型
        Integer onlineStoreType = MapUtils.getInteger(orderInfo, EXTRA_APPEND_FIELD_SOURCE_PLATFORM);
        // 会员信息
        HashMap<String, Object> memberInfo = getMapObject(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_MEMBER_INFO);
        // 额外字段
        HashMap<String, Object> extendField = getMapObject(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_EXTEND_FIELD);
        HashMap<String, Object> indexField = getMapObject(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_INDEX_FIELD);
        // 配送信息
        HashMap<String, Object> shipping = getMapObject(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_SHIPPING);
        // 推广信息
        PromotionDto promotionDto = getPromotionDto(extendField);

        // 获取付款信息
        List<PaymentDto> paymentDtoList = getPaymentDto(orderInfo);
            // 根据不同的平台使用不同的oaid获取方式（京东、淘宝、小红书、唯品会从extend_fields获取, momo本地加解密，其他从index_fields获取）
        boolean useExtend = Objects.equals(onlineStoreType, CommonConst.SOURCE_PLATFORM_JD) ||
                Objects.equals(onlineStoreType, CommonConst.SOURCE_PLATFORM_TB) ||
                Objects.equals(onlineStoreType, CommonConst.SOURCE_PLATFORM_WPH) ||
                Objects.equals(onlineStoreType, CommonConst.SOURCE_PLATFORM_XHS);
        // 获取买家信息
        String oaid = OrderDecryptConversionHandle.getOaid(onlineStoreType, useExtend ? extendField :
                Objects.equals(onlineStoreType, CommonConst.SOURCE_PLATFORM_MOMO) ? getMapObject(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_CONSIGNEE) :
                        indexField);
        BuyerDto buyerDto = getBuyerDto(onlineStoreType, oaid, orderInfo);
        // 获取商品信息
        List<GoodsDto> goodsDtoList = getGoodsDto(orderInfo, extendField, onlineStoreType);
        // 获取优惠信息
        List<DiscountDto> discountDtoList = getDiscountDto(orderInfo);
        // 发票信息
        InvoiceDto invoiceDto = getInvoiceDto(orderInfo, indexField);
        // 不重要的信息，转成json
//        getMapObject(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_YFX_INFO);
        getMapObject(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_O2O_INFO);
        getMapObject(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_SERVICE_ORDER_OBJECTS);
        getMapObject(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_PAY_INFO);
        getMapList(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_SERVICE_OTHER_LIST);
        getMapObject(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_SELF_FETCH_INFO);
        getMapObject(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_PAYMENT_DETAIL);

        // 订单类型
        conversionOrderType(retailOrderDto, orderInfo, onlineStoreType);

        retailOrderDto.setPlatformOrderNumber(MapUtils.getString(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_TID, StringUtils.EMPTY));
        retailOrderDto.setOrderTime(getMapLong(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_CREATE_TIME) * 1000);
        retailOrderDto.setSigningTimeStr(MapUtils.getString(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_END_TIME, StringUtils.EMPTY));
        retailOrderDto.setSigningTime(paresDateStrToLongUnix(retailOrderDto.getSigningTimeStr(), FMT_sdf14_L));
        retailOrderDto.setLastModifiedTime(getLastModifiedTime(orderInfo));
        retailOrderDto.setDownloadTime(getMapLong(orderInfo, OriginalOrderFieldConst.EXTRA_APPEND_FIELD_ORDER_DOWN_TIME));
        retailOrderDto.setOnlineStoreCode(MapUtils.getString(orderInfo, OriginalOrderFieldConst.EXTRA_APPEND_FIELD_STORE_CODE, StringUtils.EMPTY));
        retailOrderDto.setOnlineStoreType(onlineStoreType);
        retailOrderDto.setBusinessModel(MapUtils.getString(orderInfo, OriginalOrderFieldConst.EXTRA_APPEND_FIELD_BUSINESS_TYPE, StringUtils.EMPTY));
        SaleTypeEnum saleTypeEnum = SaleTypeEnum.getSaleTypeEnumByCode(MapUtils.getString(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_T_TYPE));
        retailOrderDto.setSalesModel(-1);
        if (saleTypeEnum != null) {
            orderInfo.put(OriginalOrderFieldConst.EXTRA_APPEND_FIELD_T_TYPE_STR, saleTypeEnum.getName());
            retailOrderDto.setSalesModel(saleTypeEnum.getValue());
        }
        retailOrderDto.setFlagId(MapUtils.getString(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_MARK_TYPE, StringUtils.EMPTY));
        retailOrderDto.setSellerMemo(MapUtils.getString(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_MARK_TEXT, StringUtils.EMPTY));
        retailOrderDto.setBuyerMemo(MapUtils.getString(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_BUYER_MEMO, StringUtils.EMPTY));
        retailOrderDto.setGoodsTotalFee(getMapBigDecimal(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_COST_ITEM));
        retailOrderDto.setShippingFee(getMapBigDecimal(shipping, ORIGINAL_ORDER_J_Z_FIELD_SHIPPING_COST_SHIPPING));
        retailOrderDto.setOrderTotalFee(getMapBigDecimal(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_TOTAL_TRADE_FEE));
        // 平台订单优惠
        BigDecimal pmtOrder = getMapBigDecimal(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_PMT_ORDER);
        // 购物金优惠
        BigDecimal expandCardExpandPriceUsed = getMapBigDecimal(extendField, ORIGINAL_ORDER_J_Z_FIELD_EXTEND_FIELD_EXPAND_CARD_EXPAND_PRICE_USED);
        retailOrderDto.setOrderDiscountFee(pmtOrder.subtract(expandCardExpandPriceUsed));
        // 订单手工调价
        retailOrderDto.setOrderManualDiscountFee(BigDecimal.ZERO);
        retailOrderDto.setPayedFee(getMapBigDecimal(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_PAYED_FEE));
        // 平台实付金额 = 订单已支付金额
        retailOrderDto.setPlatformPayedFee(getMapBigDecimal(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_PAYED_FEE));
        retailOrderDto.setProtectFee(getMapBigDecimal(shipping, ORIGINAL_ORDER_J_Z_FIELD_SHIPPING_COST_PROTECT));
        if (retailOrderDto.getProtectFee().compareTo(BigDecimal.ZERO) > 0) {
            orderInfo.put(OriginalOrderFieldConst.EXTRA_APPEND_FIELD_IS_COST_PROTECT, true);
        } else {
            orderInfo.put(OriginalOrderFieldConst.EXTRA_APPEND_FIELD_IS_COST_PROTECT, false);
        }
        // 付款状态 默认就是全额支付
        retailOrderDto.setPaymentStatus(CommonConst.PAYMENT_STATUS_FULL);
        orderInfo.put(OriginalOrderFieldConst.EXTRA_APPEND_FIELD_PAYMENT_STATUS_STR, "全额支付");
        TransactionStatusEnum transactionStatusEnum = TransactionStatusEnum.getTransactionStatusEnumByCode(MapUtils.getString(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_STATUS));
        retailOrderDto.setTradingStatus(0);
        if (transactionStatusEnum != null) {
            retailOrderDto.setTradingStatus(transactionStatusEnum.getValue());
            orderInfo.put(OriginalOrderFieldConst.EXTRA_APPEND_FIELD_TRADING_STATUS_STR, transactionStatusEnum.getName());
        }
        retailOrderDto.setPaymentType(getPaymentType(MapUtils.getBoolean(shipping, ORIGINAL_ORDER_J_Z_FIELD_SHIPPING_IS_COD)));
        retailOrderDto.setIsInvoice(getIsInvoice(MapUtils.getBoolean(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_IS_TAX)));
        retailOrderDto.setEstimatedTotalWeight(getMapBigDecimal(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_WEIGHT));
        retailOrderDto.setBaseBuyerDto(buyerDto);
        retailOrderDto.setBasePaymentDtoList(paymentDtoList);
        retailOrderDto.setBaseGoodsDtoList(goodsDtoList);
        retailOrderDto.setBaseDiscountDtoList(discountDtoList);
        retailOrderDto.setBaseInvoice(invoiceDto);
        retailOrderDto.setMemberId(MapUtils.getString(memberInfo, ORIGINAL_ORDER_J_Z_FIELD_MEMBER_INFO_UNAME, StringUtils.EMPTY));
        retailOrderDto.setBuyerOpenUid(MapUtils.getString(memberInfo, ORIGINAL_ORDER_J_Z_FIELD_MEMBER_INFO_BUYER_OPEN_UID, StringUtils.EMPTY));
        retailOrderDto.setIsShShip(MapUtils.getBoolean(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_IS_SH_SHIP));
        // 指定物流公司
        retailOrderDto.setPreDesignatedLogisticsCode(getPreDesignatedLogisticsCode(orderInfo));
        // 是否风控订单
        retailOrderDto.setIsRisk(MapUtils.getBoolean(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_IS_RISK, false));
        // 预先指定配送方式 TODO
        retailOrderDto.setPreDesignatedStoreDelivery(MapUtils.getInteger(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_PRE_DESIGNATED_STORE_DELIVERY, 0));
        // 预先指定门店
        retailOrderDto.setPreDesignatedStoreCode(MapUtils.getString(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_PRE_DESIGNATED_STORE_CODE, StringUtils.EMPTY));
        // 推广相关字段
        if (Objects.nonNull(promotionDto)) {
            retailOrderDto.setPromotionChannelCode(promotionDto.getPromotionChannelCode());
            retailOrderDto.setSalesmanCode(promotionDto.getSalesmanCode());
        }
        if (Objects.equals(retailOrderDto.getOnlineStoreType(), CommonConst.SOURCE_PLATFORM_JD)) {
            retailOrderDto.setParentPlatformOrderNumber(MapUtils.getString(extendField, "directParentOrderId", StringUtils.EMPTY));
            retailOrderDto.setRootPlatformOrderNumber(MapUtils.getString(extendField, "parentOrderId", StringUtils.EMPTY));
        }

        // 补充字段
        supplementaryFiled(retailOrderDto, orderInfo);

        // 原始订单表需要字段
        retailOrderDto.setSourceStatus(MapUtils.getString(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_STATUS, StringUtils.EMPTY));
        retailOrderDto.setTransactionTitle(MapUtils.getString(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_TITLE, StringUtils.EMPTY));
        // 交易类型（原始订单上存原始数据）
        retailOrderDto.setTransactionType(MapUtils.getString(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_TRADE_TYPE, StringUtils.EMPTY));
        if (Objects.equals(retailOrderDto.getTransactionType(), TransactionType.STEP.code)) {
            orderInfo.put(OriginalOrderFieldConst.EXTRA_APPEND_FIELD_TRADE_TYPE_STR, TransactionType.STEP.name);
        }
        retailOrderDto.setCompletionTime(MapUtils.getString(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_END_TIME, StringUtils.EMPTY));

        if (retailOrderDto.getOrderTime() != null && retailOrderDto.getOrderTime() > 0) {
            orderInfo.put(OriginalOrderFieldConst.EXTRA_APPEND_FIELD_CREATE_TIME_STR, DateUtil.timeToString(retailOrderDto.getOrderTime(), DateUtil.FMT_sdf14_L));
        }
        if (retailOrderDto.getLastModifiedTime() != null && retailOrderDto.getLastModifiedTime() > 0) {
            orderInfo.put(OriginalOrderFieldConst.EXTRA_APPEND_FIELD_MODIFIED_STR, DateUtil.timeToString(retailOrderDto.getLastModifiedTime(), DateUtil.FMT_sdf14_L));
        }
        retailOrderDto.setIsOnlyCreateRetailOrder(false);
        if (Objects.equals(PropertiesConst.DELIVERY_SHEET_CONVERSION_SCHEME, CommonConst.DELIVERY_SHEET_CONVERSION_SCHEME_2)) {
            retailOrderDto.setIsOnlyCreateRetailOrder(true);
        }
        //判断是否偏远中转订单
        List<HashMap<String, Object>> platformOrderTagUi = null;
        try {
            platformOrderTagUi = getMapList(extendField, "platform_order_tag_ui");
            if (CollectionUtils.isNotEmpty(platformOrderTagUi)) {
                // 判断是否存在key=logistics_transit的对象且存在text=偏远中转的对象
                boolean hasLogisticsTransit = platformOrderTagUi.stream()
                        .anyMatch(map -> "logistics_transit".equals(MapUtils.getString(map, "key", "")) &&
                        "偏远中转".equals(MapUtils.getString(map, "text", "")));
                retailOrderDto.setIsLogisticsTransit(hasLogisticsTransit);
            }
        } catch (Exception e) {
            log.warn("解析平台订单标签UI信息失败", e);
        }
        // 是否送礼订单
        Boolean isPresent = MapUtils.getBoolean(extendField, ORIGINAL_ORDER_J_Z_FIELD_IS_PRESENT, Boolean.FALSE);
        retailOrderDto.setIsPresent(isPresent);
        return retailOrderDto;
    }

    /**
     * 推销员信息
     */
    private static PromotionDto getPromotionDto(HashMap<String, Object> extendField) {
        try {
            List<HashMap<String, Object>> promotionInfos = getMapList(extendField, ORIGINAL_ORDER_J_Z_FIELD_PROMOTION_INFO);
            if (CollectionUtils.isNotEmpty(promotionInfos)) {
                HashMap<String, Object> map = promotionInfos.get(0);
                PromotionDto promotionDto = new PromotionDto();
                promotionDto.setPromotionChannelCode(MapUtils.getString(map, ORIGINAL_ORDER_J_Z_FIELD_PROMOTION_INFO_CHANNEL_CODE, StringUtils.EMPTY));
                promotionDto.setSalesmanCode(MapUtils.getString(map, ORIGINAL_ORDER_J_Z_FIELD_PROMOTION_INFO_SALESMAN_CODE, StringUtils.EMPTY));
                return promotionDto;
            }
        } catch (JsonProcessingException e) {
            log.warn("解析推销员信息失败", e);
        }
        return null;
    }

    /**
     * 获取发票实体
     *
     * @param orderInfo  订单信息
     * @param indexField indexField
     * @return 发票实体
     */
    private static InvoiceDto getInvoiceDto(Map<String, Object> orderInfo, HashMap<String, Object> indexField) {
        Integer platformOrderInvoiceKind = MapUtils.getInteger(indexField, ORIGINAL_ORDER_J_Z_FIELD_INVOICE_KIND, 0);
        if (Objects.equals(platformOrderInvoiceKind, JD_INVOICE_KIND_NOT)) {
            log.info("订单发票类型为0-不开票");
            return null;
        }
        Integer invoiceKind = getInvoiceKind(platformOrderInvoiceKind);
        if (Objects.isNull(invoiceKind)) {
            log.info("订单发票类型转换错误 平台订单号: {}, 平台订单发票类型: {}", MapUtils.getString(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_TID, StringUtils.EMPTY), platformOrderInvoiceKind);
            return null;
        }
        // 京东逻辑
        if (Objects.equals(MapUtils.getInteger(orderInfo, OriginalOrderFieldConst.EXTRA_APPEND_FIELD_SOURCE_PLATFORM), CommonConst.SOURCE_PLATFORM_JD)) {
            InvoiceDto invoiceDto = new InvoiceDto();
            invoiceDto.setPlatformOrderNumber(MapUtils.getString(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_TID, StringUtils.EMPTY));
            invoiceDto.setInvoiceKind(invoiceKind);
            invoiceDto.setInvoiceAddress(MapUtils.getString(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_INVOICE_ADDRESS, StringUtils.EMPTY));
            invoiceDto.setInvoiceBankAccount(MapUtils.getString(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_INVOICE_BANK_ACCOUNT, StringUtils.EMPTY));
            invoiceDto.setInvoiceBankName(MapUtils.getString(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_INVOICE_BANK_NAME, StringUtils.EMPTY));
            invoiceDto.setInvoicePhone(MapUtils.getString(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_INVOICE_PHONE, StringUtils.EMPTY));
            invoiceDto.setTaxTitle(MapUtils.getString(indexField, ORIGINAL_ORDER_J_Z_FIELD_TAX_TITLE, StringUtils.EMPTY));
            invoiceDto.setInvoiceConsigneeEmail(MapUtils.getString(indexField, ORIGINAL_ORDER_J_Z_FIELD_INVOICE_CONSIGNEE_EMAIL, StringUtils.EMPTY));
            invoiceDto.setInvoiceConsigneePhone(MapUtils.getString(indexField, ORIGINAL_ORDER_J_Z_FIELD_INVOICE_CONSIGNEE_PHONE, StringUtils.EMPTY));
            invoiceDto.setPayerRegisterNoIndex(MapUtils.getString(indexField, ORIGINAL_ORDER_J_Z_FIELD_INDEX_FIELD_PAYER_REGISTER_NO_INDEX, StringUtils.EMPTY));
            invoiceDto.setInvoiceBuyerAddressIndex(MapUtils.getString(indexField, ORIGINAL_ORDER_J_Z_FIELD_INDEX_FIELD_INVOICE_BUYER_ADDRESS_INDEX, StringUtils.EMPTY));
            invoiceDto.setInvoiceBuyerPhoneIndex(MapUtils.getString(indexField, ORIGINAL_ORDER_J_Z_FIELD_INDEX_FIELD_INVOICE_BUYER_PHONE_INDEX, StringUtils.EMPTY));
            invoiceDto.setReceiverNameIndex(MapUtils.getString(indexField, ORIGINAL_ORDER_J_Z_FIELD_INDEX_FIELD_RECEIVER_NAME_INDEX, StringUtils.EMPTY));
            return invoiceDto;
        }
        return null;
    }

    /**
     * 获取发票类型
     *
     * @param platformOrderInvoiceKind 平台发订单发票类型
     * @return 发票类型
     */
    private static Integer getInvoiceKind(Integer platformOrderInvoiceKind) {
        if (Objects.equals(platformOrderInvoiceKind, JD_INVOICE_KIND_PAPER)) {
            return CommonConst.PLATFORM_INVOICE_KIND_PAPER;
        } else if (Objects.equals(platformOrderInvoiceKind, JD_INVOICE_KIND_PAPER_SPECIAL)) {
            return CommonConst.PLATFORM_INVOICE_KIND_SPECIAL;
        } else if (Objects.equals(platformOrderInvoiceKind, JD_INVOICE_KIND_ELECTRON)) {
            return CommonConst.PLATFORM_INVOICE_KIND_ELECTRON;
        }
        return 0;
    }

    /**
     * 获取收件人信息
     *
     * @param orderInfo 原始订单信息
     * @return 收件人信息
     */
    private static BuyerDto getBuyerDto(Integer onlineStoreType, String oaid, Map<String, Object> orderInfo) throws JsonProcessingException {
        HashMap<String, Object> consignee = getMapObject(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_CONSIGNEE);
        if (MapUtils.isEmpty(consignee)) {
            // 拼多多订单 收货人信息可能为空
            return null;
        }
        // 姓名
        String name = MapUtils.getString(consignee, ORIGINAL_ORDER_J_Z_FIELD_CONSIGNEE_CONSIGNEE_NAME, StringUtils.EMPTY);
        // 电话
        String tele = Objects.equals(MapUtils.getInteger(orderInfo, OriginalOrderFieldConst.EXTRA_APPEND_FIELD_SOURCE_PLATFORM), CommonConst.SOURCE_PLATFORM_JD) ? StringUtils.EMPTY : MapUtils.getString(consignee, ORIGINAL_ORDER_J_Z_FIELD_CONSIGNEE_CONSIGNEE_TELEPHONE, StringUtils.EMPTY);
        // 手机
        String mobile = MapUtils.getString(consignee, ORIGINAL_ORDER_J_Z_FIELD_CONSIGNEE_CONSIGNEE_MOBILE, StringUtils.EMPTY);
        // 地址
        String address = MapUtils.getString(consignee, ORIGINAL_ORDER_J_Z_FIELD_CONSIGNEE_ADDR, StringUtils.EMPTY);

        BuyerDto buyerDto = new BuyerDto();
        buyerDto.setCountry(MapUtils.getString(consignee, "country", StringUtils.EMPTY));
        buyerDto.setProvince(MapUtils.getString(consignee, ORIGINAL_ORDER_J_Z_FIELD_CONSIGNEE_AREA_STATE, StringUtils.EMPTY));
        buyerDto.setCity(MapUtils.getString(consignee, ORIGINAL_ORDER_J_Z_FIELD_CONSIGNEE_AREA_CITY, StringUtils.EMPTY));
        buyerDto.setArea(MapUtils.getString(consignee, ORIGINAL_ORDER_J_Z_FIELD_CONSIGNEE_AREA_DISTRICT, StringUtils.EMPTY));
        buyerDto.setStreet(MapUtils.getString(consignee, ORIGINAL_ORDER_J_Z_FIELD_CONSIGNEE_AREA_STREET, StringUtils.EMPTY));
        buyerDto.setZipCode(MapUtils.getString(consignee, ORIGINAL_ORDER_J_Z_FIELD_CONSIGNEE_ZIP, StringUtils.EMPTY));
        if (onlineStoreType >= CommonConst.SOURCE_PLATFORM_EZR) {
            // 私域平台自己加密
            buyerDto.setName(DesensitizationUtil.desensitizedChineseName(name));
            buyerDto.setTele(DesensitizationUtil.desensitizedChineseName(tele));
            buyerDto.setMobile(DesensitizationUtil.desensitizedChineseName(mobile));
            buyerDto.setAddress(DesensitizationUtil.desensitizedChineseName(address));
        } else if (CommonConst.SOURCE_PLATFORM_WPH.equals(onlineStoreType)) {
            // 唯品会进行局部脱敏，仅脱敏地址
            buyerDto.setName(name);
            buyerDto.setTele(tele);
            buyerDto.setMobile(mobile);
            buyerDto.setAddress(DesensitizationUtil.desensitizeAddress(address));
        }  else if (CommonConst.SOURCE_PLATFORM_MOMO.equals(onlineStoreType)) {
            // MOMO
            buyerDto.setName(DesensitizationUtil.desensitizedChineseName(name));
            buyerDto.setTele(tele);
            buyerDto.setMobile(mobile);
            buyerDto.setAddress(DesensitizationUtil.desensitizeAddress(address));
        } else {
            buyerDto.setName(name);
            buyerDto.setTele(tele);
            buyerDto.setMobile(mobile);
            buyerDto.setAddress(address);
        }
        buyerDto.setOaid(oaid);
        return buyerDto;
    }

    /**
     * 获取支付信息
     *
     * @param orderInfo 原始订单信息
     * @return 支付信息
     */
    private static List<PaymentDto> getPaymentDto(Map<String, Object> orderInfo) throws JsonProcessingException {
        List<PaymentDto> paymentDtoList = new ArrayList<>();
        List<HashMap<String, Object>> payments = getMapList(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_PAYMENT);
        if (CollectionUtils.isNotEmpty(payments)) {
            for (HashMap<String, Object> payment : payments) {
                PaymentDto paymentDto = new PaymentDto();
                paymentDto.setPaymentTimeStr(MapUtils.getString(payment, ORIGINAL_ORDER_J_Z_FIELD_PAYMENT_PAY_TIME, StringUtils.EMPTY));
                paymentDto.setPaymentTime(paresDateStrToLongUnix(paymentDto.getPaymentTimeStr(), FMT_sdf14_L));
                paymentDto.setPlatformPaymentSlipNumber(MapUtils.getString(payment, ORIGINAL_ORDER_J_Z_FIELD_PAYMENT_TRADE_NO, StringUtils.EMPTY));
                paymentDto.setPayCost(getMapBigDecimal(payment, ORIGINAL_ORDER_J_Z_FIELD_PAYMENT_MONEY));
                paymentDto.setPaymentMethodId(MapUtils.getString(payment, ORIGINAL_ORDER_J_Z_FIELD_PAYMENT_PAY_BN, StringUtils.EMPTY));
                paymentDto.setPaymentMethod(MapUtils.getString(payment, ORIGINAL_ORDER_J_Z_FIELD_PAYMENT_PAYMETHOD, StringUtils.EMPTY));
                String currency = MapUtils.getString(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_CURRENCY, StringUtils.EMPTY);
                paymentDto.setCurrency(StringUtils.isNotBlank(currency) ? currency : CNY);
                paymentDto.setCurrencySymbol(getCurrencySymbol(paymentDto.getCurrency()));
                paymentDtoList.add(paymentDto);
            }
        }
        return paymentDtoList;
    }

    /**
     * 获取优惠信息
     *
     * @param orderInfo 原始订单信息
     * @return 优惠信息
     */
    private static List<DiscountDto> getDiscountDto(Map<String, Object> orderInfo) throws JsonProcessingException {
        List<DiscountDto> discountDtoList = new ArrayList<>();
        List<HashMap<String, Object>> pmtDetails = getMapList(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_PMT_DETAIL);
        if (CollectionUtils.isNotEmpty(pmtDetails)) {
            for (HashMap<String, Object> pmtDetail : pmtDetails) {
                DiscountDto discountDto = new DiscountDto();
                // 优惠券id 当前版本可能没有，矩阵后续会加上
                discountDto.setCouponId(MapUtils.getString(pmtDetail, ORIGINAL_ORDER_J_Z_FIELD_PMT_DETAIL_PROMOTION_ID, StringUtils.EMPTY));
                discountDto.setDiscountCost(getMapBigDecimal(pmtDetail, ORIGINAL_ORDER_J_Z_FIELD_PMT_DETAIL_PMT_AMOUNT));
                discountDto.setDiscountName(MapUtils.getString(pmtDetail, ORIGINAL_ORDER_J_Z_FIELD_PMT_DETAIL_PMT_NAME, StringUtils.EMPTY));
                discountDto.setDiscountDescription(MapUtils.getString(pmtDetail, ORIGINAL_ORDER_J_Z_FIELD_PMT_DETAIL_PMT_DESCRIBE, StringUtils.EMPTY));
                // 优惠级别 暂时没有 产品已确认
                discountDto.setDiscountLevel(0);
                // 优惠类型 拿不到这个字段 产品已确认
                discountDto.setDiscountType(StringUtils.EMPTY);
                // 子单号 暂时当作子单号字段，但是矩阵有一层逻辑，有可能回传tid或者id，这个到时候可以让矩阵改下打给我们的逻辑 产品已确认
                discountDto.setOrderNumber(MapUtils.getString(pmtDetail, ORIGINAL_ORDER_J_Z_FIELD_PMT_DETAIL_PMT_ID, StringUtils.EMPTY));
                discountDtoList.add(discountDto);
            }
        }
        return discountDtoList;
    }

    /**
     * 获取商品信息
     *
     * @param orderInfo 原始订单信息
     * @return 商品信息
     */
    private static List<GoodsDto> getGoodsDto(Map<String, Object> orderInfo, HashMap<String, Object> extendField, Integer onlineStoreType) throws JsonProcessingException {
        List<GoodsDto> goodsDtoList = new ArrayList<>();
        // 商品总数量
        int countBuyQuantity = 0;
        // 子订单信息
        List<HashMap<String, Object>> orderObjects = getMapList(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_ORDER_OBJECTS);
        // 全部子单号
        List<String> oidList = orderObjects.stream().map(map -> MapUtils.getString(map, ORIGINAL_ORDER_J_Z_FIELD_ORDER_OBJECTS_OID, StringUtils.EMPTY)).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(orderObjects)) {
            // 组合商品信息
            Map<String, List<HashMap<String, Object>>> combinationSkuInfo = null;
            // 淘宝、抖音、拼多多赠品信息
            Map<String, String> giftInfo = null;
            // 组合商品信息解析
            List<HashMap<String, Object>> extendOrderObjects = getMapList(extendField, ORIGINAL_ORDER_J_Z_FIELD_ORDER_OBJECTS);
            if (CollectionUtils.isNotEmpty(extendOrderObjects)) {
                combinationSkuInfo = Maps.newHashMap();
                for (HashMap<String, Object> extendOrderObject : extendOrderObjects) {
                    // 平台订单号
                    String oid = MapUtils.getString(extendOrderObject, ORIGINAL_ORDER_J_Z_FIELD_ORDER_OBJECTS_OID);
                    // 组合SKU信息
                    List<HashMap<String, Object>> combinationGoodsList = getMapList(extendOrderObject, DY_ORIGINAL_ORDER_J_Z_FIELD_EXTEND_FIELD_BUNDLE_SKU_INFO);
                    combinationSkuInfo.put(oid, combinationGoodsList);
                }
            }
            // 赠品信息解析
            List<HashMap<String, Object>> isFreeGiftListMap = getMapList(extendField, DY_ORIGINAL_ORDER_J_Z_FIELD_EXTEND_FIELD_IS_FREE_GIFT);
            if (CollectionUtils.isNotEmpty(isFreeGiftListMap)) {
                List<HashMap<String, Object>> giftMidListMap = getMapList(extendField, DY_ORIGINAL_ORDER_J_Z_FIELD_EXTEND_FIELD_GIFT_MIDS);
                giftInfo = Maps.newHashMap();
                for (HashMap<String, Object> map : isFreeGiftListMap) {
                    for (String oid : oidList) {
                        Object obj = map.get(oid);
                        if (obj != null) {
                            // 判断是否为赠品
                            String value = String.valueOf(obj);
                            if (Boolean.parseBoolean(value)) {
                                // 获取赠品关联主品
                                String giftMidOid = giftMidListMap.stream().filter(m -> m.containsKey(oid)).map(m -> MapUtils.getString(m, oid, StringUtils.EMPTY)).findFirst().orElse(StringUtils.EMPTY);
                                giftInfo.put(oid, giftMidOid);
                            }
                        }
                    }
                }
            }
            log.debug("组合商品信息: {}", combinationSkuInfo);
            log.debug("赠品信息: {}", giftInfo);
            for (HashMap<String, Object> orderObject : orderObjects) {
                // 平台子单号
                String oid = MapUtils.getString(orderObject, ORIGINAL_ORDER_J_Z_FIELD_ORDER_OBJECTS_OID, StringUtils.EMPTY);
                // 子订单属性列表
                getMapObject(orderObject, ORIGINAL_ORDER_J_Z_FIELD_ORDER_OBJECTS_ORDER_ATTR);
                // 子商品信息
                List<HashMap<String, Object>> orderObjectItems = new ObjectMapper().readValue(new ObjectMapper().writeValueAsString(orderObject.get(ORIGINAL_ORDER_J_Z_FIELD_ORDER_OBJECTS_ORDER_ITEMS)), new TypeReference<List<HashMap<String, Object>>>() {
                });
                String platformSkuId = "";
                String platformSkuPropertiesStr = "";
                String skuUuid = "";
                // 优惠金额统计 一个自订单下只会有一个子商品 产品已确认
                BigDecimal totalDiscountFee = BigDecimal.ZERO;
                BigDecimal total_expand_card_expand_price_used_suborder = BigDecimal.ZERO;
                if (CollectionUtils.isNotEmpty(orderObjectItems)) {
                    for (HashMap<String, Object> orderObjectItem : orderObjectItems) {
                        totalDiscountFee = totalDiscountFee.add(getMapBigDecimal(orderObjectItem, ORIGINAL_ORDER_J_Z_FIELD_ORDER_OBJECTS_ORDER_ITEMS_PMT_PRICE));
                        total_expand_card_expand_price_used_suborder = total_expand_card_expand_price_used_suborder.add(getMapBigDecimal(orderObjectItem, ORIGINAL_ORDER_J_Z_FIELD_ORDER_OBJECTS_ORDER_ITEMS_EXPAND_CARD_EXPAND_PRICE_USED_SUBORDER));
                        platformSkuId = MapUtils.getString(orderObjectItem, ORIGINAL_ORDER_J_Z_FIELD_ORDER_OBJECTS_ORDER_ITEMS_SHOP_PRODUCT_ID, "");
                        platformSkuPropertiesStr = MapUtils.getString(orderObjectItem, ORIGINAL_ORDER_J_Z_FIELD_ORDER_OBJECTS_ORDER_ITEMS_PROPERTIES_STR, StringUtils.EMPTY);
                        HashMap<String, Object> extend_item_list = getMapObject(orderObjectItem, ORIGINAL_ORDER_J_Z_FIELD_ORDER_OBJECTS_ORDER_ITEMS_EXTEND_ITEM_LIST);
                        if (MapUtils.isNotEmpty(extend_item_list)) {
                            skuUuid = MapUtils.getString(extend_item_list, "skuUuid", StringUtils.EMPTY);
                        }
                    }
                }
                GoodsDto goodsDto = new GoodsDto();
                goodsDto.setSkuCode(MapUtils.getString(orderObject, ORIGINAL_ORDER_J_Z_FIELD_ORDER_OBJECTS_BN, StringUtils.EMPTY));
                goodsDto.setPlatformGoodsId(MapUtils.getString(orderObject, ORIGINAL_ORDER_J_Z_FIELD_ORDER_OBJECTS_SHOP_GOODS_ID, StringUtils.EMPTY));
                goodsDto.setPlatformGoodsName(MapUtils.getString(orderObject, ORIGINAL_ORDER_J_Z_FIELD_ORDER_OBJECTS_SHOP_GOODS_NAME, StringUtils.EMPTY));
                if (CommonConst.SOURCE_PLATFORM_XHS.equals(onlineStoreType)) {
                    // 小红书从【shop_goods_id】获取平台的商品SKUID
                    goodsDto.setPlatformSkuId(MapUtils.getString(orderObject, ORIGINAL_ORDER_J_Z_FIELD_ORDER_OBJECTS_SHOP_GOODS_ID, StringUtils.EMPTY));
                    goodsDto.setPlatformSkuPropertiesStr(platformSkuPropertiesStr);
                } else {
                    goodsDto.setPlatformSkuId(platformSkuId);
                    goodsDto.setPlatformSkuPropertiesStr(platformSkuPropertiesStr);
                }
                goodsDto.setSellPrice(getMapBigDecimal(orderObject, ORIGINAL_ORDER_J_Z_FIELD_ORDER_OBJECTS_PRICE));
                goodsDto.setDiscountFee(totalDiscountFee.add(total_expand_card_expand_price_used_suborder));
                // 平台分销单编号
                extractPoNo(oid, goodsDto, extendField);
                // 达人信息
                extractSalesman(oid, goodsDto, extendField);
                // 商品手工调价
                goodsDto.setManualDiscountFee(getMapBigDecimal(orderObject, ORIGINAL_ORDER_J_Z_FIELD_ORDER_OBJECTS_ADJUST_FEE));
                goodsDto.setDiscountAllocation(getMapBigDecimal(orderObject, ORIGINAL_ORDER_J_Z_FIELD_ORDER_OBJECTS_PART_MJZ_DISCOUNT));
                goodsDto.setTransactionFee(getMapBigDecimal(orderObject, ORIGINAL_ORDER_J_Z_FIELD_ORDER_OBJECTS_DIVIDE_ORDER_FEE).subtract(total_expand_card_expand_price_used_suborder));
                goodsDto.setBuyQuantity(MapUtils.getInteger(orderObject, ORIGINAL_ORDER_J_Z_FIELD_ORDER_OBJECTS_QUANTITY));
                goodsDto.setSkuUuid(skuUuid);
                goodsDto.setSubOrderNumber(oid);
                TransactionStatusEnum transactionStatusEnum = TransactionStatusEnum.getTransactionStatusEnumByCode(MapUtils.getString(orderObject, ORIGINAL_ORDER_J_Z_FIELD_ORDER_OBJECTS_SOURCE_STATUS));
                goodsDto.setSubOrderStatus(transactionStatusEnum == null ? 0 : transactionStatusEnum.getValue());
                // 预计发货时间 平台有
                goodsDto.setConsignTimeStr(MapUtils.getString(orderObject, ORIGINAL_ORDER_J_Z_FIELD_ORDER_OBJECTS_ESTIMATE_CON_TIME, StringUtils.EMPTY));
                goodsDto.setEstimatedDeliveryTime(paresDateStrToLongUnix(goodsDto.getConsignTimeStr(), FMT_sdf14_L));

                // 由于识别的方式太多，现与客户确认，可以将该0元商品转单进入订单/发货单时，标记平台赠品，不关联主品 产品确认
                if (goodsDto.getTransactionFee().compareTo(BigDecimal.ZERO) == 0) {
                    goodsDto.setIsGift(true);
                }

                // 组合商品
                if (MapUtils.isNotEmpty(combinationSkuInfo)) {
                    List<HashMap<String, Object>> mapList = combinationSkuInfo.get(oid);
                    if (CollectionUtils.isNotEmpty(mapList)) {
                        goodsDto.setCombinationGoods(new ArrayList<>());
                        Map<String, Integer> combinationGoodsMap = Maps.newHashMap();
                        mapList.forEach(combinationGoods -> {
                            String code = MapUtils.getString(combinationGoods, DY_ORIGINAL_ORDER_J_Z_FIELD_EXTEND_FIELD_BUNDLE_SKU_INFO_CODE, StringUtils.EMPTY);
                            Integer num = MapUtils.getInteger(combinationGoods, DY_ORIGINAL_ORDER_J_Z_FIELD_EXTEND_FIELD_BUNDLE_SKU_INFO_ITEM_NUM);
                            if (!combinationGoodsMap.containsKey(code)) {
                                combinationGoodsMap.put(code, 0);
                            }
                            combinationGoodsMap.put(code, combinationGoodsMap.get(code) + num);
                        });
                        combinationGoodsMap.forEach((k, v) -> goodsDto.getCombinationGoods().add(new CombinationGoodsDto(k, v)));
                    }
                }
                // 赠品信息
                if (MapUtils.isNotEmpty(giftInfo)) {
                    String masterOid = giftInfo.get(oid);
                    if (Objects.nonNull(masterOid)) {
                        goodsDto.setIsGift(true);
                        goodsDto.setGiftMasterSubOrderNumber(masterOid);
                    }
                }
                goodsDto.setPreDesignatedWarehouseCode(StringUtils.EMPTY);
                // 预先指定物流 没有该字段 TODO
                goodsDto.setPreDesignatedLogisticsCode(StringUtils.EMPTY);
                // 预先指定门店 平台有，矩阵未返回 TODO
                goodsDto.setPreDesignatedStoreCode(StringUtils.EMPTY);
                // 预先指定门店的配送方式 没有该字段 TODO
                goodsDto.setPreDesignatedStoreDelivery(0);
                goodsDtoList.add(goodsDto);
                countBuyQuantity += goodsDto.getBuyQuantity();
            }
        }
        orderInfo.put(EXTRA_APPEND_FIELD_ORDER_GOODS_QUANTITY, countBuyQuantity);
        return goodsDtoList;
    }

    /**
     * 从订单扩展信息中获取【平台分销单编号】
     *
     * @param oid         子单ID
     * @param goodsDto    商品信息
     * @param extendField 扩展信息
     * @throws JsonProcessingException
     */
    private static void extractPoNo(String oid, GoodsDto goodsDto, Map<String, Object> extendField) throws JsonProcessingException {

        // 扩展信息 - 子单扩展信息列表
        List<HashMap<String, Object>> extendOidInfoList = getMapList(extendField, ORIGINAL_ORDER_J_Z_FIELD_EXTEND_FIELD_OID_INFO_LIST);
        Optional<HashMap<String, Object>> opnOidInfo = extendOidInfoList.stream().filter(v -> Objects.equals(oid, v.get(ORIGINAL_ORDER_J_Z_FIELD_EXTEND_FIELD_OID_INFO_OID))).findFirst();
        if (opnOidInfo.isPresent()) {
            HashMap<String, Object> mapOidInfo = opnOidInfo.get();
            goodsDto.setPlatformPurchaseOrderNumber(MapUtils.getString(mapOidInfo, ORIGINAL_ORDER_J_Z_FIELD_EXTEND_FIELD_OID_INFO_PO_NO, StringUtils.EMPTY));
        }
    }

    /**
     * 从订单扩展信息中获取【达人信息】
     *
     * @param oid         子单ID
     * @param goodsDto    商品信息
     * @param extendField 扩展信息
     * @throws JsonProcessingException
     */
    private static void extractSalesman(String oid, GoodsDto goodsDto, Map<String, Object> extendField) throws JsonProcessingException {
        HashMap<String, Object> salesmanMap = getMapObject(extendField, ORIGINAL_ORDER_J_Z_FIELD_EXTEND_FIELD_OID_INFO_AUTHOR_INFO);
        System.out.println();
        log.debug("从订单扩展信息中获取【达人信息Map】: {}", salesmanMap);
        if (MapUtils.isNotEmpty(salesmanMap)) {
            if (Objects.nonNull(salesmanMap.get(oid))) {
                Map<String, Object> map = getMapObject(salesmanMap, oid);
                goodsDto.setSalesmanCode(MapUtils.getString(map, ORIGINAL_ORDER_J_Z_FIELD_EXTEND_FIELD_OID_INFO_AUTHOR_ID, StringUtils.EMPTY));
                goodsDto.setSalesmanName(MapUtils.getString(map, ORIGINAL_ORDER_J_Z_FIELD_EXTEND_FIELD_OID_INFO_AUTHOR_NAME, StringUtils.EMPTY));
            }
        }
//        // 扩展信息 - 子单扩展信息列表
//        String authorInfo = MapUtils.getString(extendField, ORIGINAL_ORDER_J_Z_FIELD_EXTEND_FIELD_OID_INFO_AUTHOR_INFO);
//        if (StringUtils.isNotEmpty(authorInfo)) {
//            ObjectMapper mapper = new ObjectMapper();
//            String data = mapper.writeValueAsString(authorInfo);
//            log.debug("从订单扩展信息中获取【达人信息data】: {}", data);
//            Map<String, Object> salesmanMap = mapper.readValue(data, new TypeReference<Map<String, Object>>() {
//            });
//            log.debug("从订单扩展信息中获取【达人信息Map】: {}", salesmanMap);
//            if (MapUtils.isNotEmpty(salesmanMap)) {
//                if (Objects.nonNull(salesmanMap.get(oid))) {
//                    Map<String, Object> map = getMapObject(salesmanMap, oid);
//                    goodsDto.setSalesmanCode(MapUtils.getString(map, ORIGINAL_ORDER_J_Z_FIELD_EXTEND_FIELD_OID_INFO_AUTHOR_ID, StringUtils.EMPTY));
//                    goodsDto.setSalesmanName(MapUtils.getString(map, ORIGINAL_ORDER_J_Z_FIELD_EXTEND_FIELD_OID_INFO_AUTHOR_NAME, StringUtils.EMPTY));
//                }
//            }
//        }
    }

    /**
     * 原始订单转换常量
     */
    protected interface OriginalOrderConversionConst {
        /***************************京东相关常量****************************/
        String JD_STORE_ORDER_JING_WAREHOUSE = "京仓订单";
        String JD_STORE_ORDER_CLOUD_WAREHOUSE = "云仓订单";
        /**
         * 京东字段值-订单类型: SOP
         */
        Integer JD_FIELD_VALUE_BUSINESS_TYPE_SOP = 22;

        /**
         * 原始订单转换补充订单类型: 京仓订单
         */
        Integer JD_SUPPLEMENTARY_ORDER_TYPE_JING_WAREHOUSE = 1;
        /**
         * 原始订单转换补充订单类型: 云仓订单
         */
        Integer JD_SUPPLEMENTARY_ORDER_TYPE_CLOUD_WAREHOUSE = 2;
        /**
         * 原始订单转换补充订单类型: 非SOP订单
         */
        Integer JD_SUPPLEMENTARY_ORDER_TYPE_NOT_SOP = 3;
        /**
         * 京东字段值-发票类型: 不开发票
         */
        Integer JD_INVOICE_KIND_NOT = 0;
        /**
         * 京东字段值-发票类型: 纸质普票
         */
        Integer JD_INVOICE_KIND_PAPER = 1;
        /**
         * 京东字段值-发票类型: 纸质专票
         */
        Integer JD_INVOICE_KIND_PAPER_SPECIAL = 2;
        /**
         * 京东字段值-发票类型: 电子普票
         */
        Integer JD_INVOICE_KIND_ELECTRON = 2;
        /***************************拼多多相关常量****************************/
        Integer PDD_ORDER_TAG_LIST_VALUE_TRUE = 1;
        /**
         * 拼多多字段值: 订单标签-顺丰加价
         */
        String PDD_ORDER_TAG_LIST_HAS_SF_EXPRESS_SERVICE = "has_sf_express_service";
    }

    /**
     * 获取货款方式常量
     *
     * @param isCod 是否货到付款
     * @return 货款方式常量
     */
    private static Integer getPaymentType(Boolean isCod) {
        return isCod == null ? 0 : isCod ? CommonConst.PAYMENT_METHOD_CASH_ON_DELIVERY : CommonConst.PAYMENT_METHOD_PAYMENT_TO_SHIPMENT;
    }

    /**
     * 获取是否开票常量
     *
     * @param isTax 是否开票
     * @return 是否开票常量
     */
    private static Integer getIsInvoice(Boolean isTax) {
        return isTax == null ? -1 : isTax ? CommonConst.COMMON_TRUE : CommonConst.COMMON_FALSE;
    }

    /**
     * 转换订单类型
     */
    private static void conversionOrderType(RetailOrderDto retailOrderDto, Map<String, Object> orderInfo, Integer onlineStoreType) {
        Boolean isYuShou = MapUtils.getBoolean(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_IS_YU_SHOU, false);
        String tradeType = MapUtils.getString(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_TRADE_TYPE, StringUtils.EMPTY);
        if (isYuShou) {
            // 全额预售
            retailOrderDto.setType(CommonConst.ORDER_TYPE_PRE);
            retailOrderDto.setPreSaleType(CommonConst.PRE_SALE_TYPE_FULL_PAYMENT);
        } else if (StringUtils.isNotBlank(tradeType) && Objects.equals(tradeType, TransactionType.STEP.code)) {
            // 定金预售
            retailOrderDto.setType(CommonConst.ORDER_TYPE_PRE);
            retailOrderDto.setPreSaleType(CommonConst.PRE_SALE_TYPE_DEPOSIT);
            // 定金预售状态
            String stepTradeStatus = MapUtils.getString(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_STEP_TRADE_STATUS, StringUtils.EMPTY);
            StepTradeStatusEnum stepTradeStatusEnum = StepTradeStatusEnum.getStepTradeStatusEnumByCode(stepTradeStatus);
            retailOrderDto.setPreSaleStatus(stepTradeStatusEnum == null ? 0 : stepTradeStatusEnum.getValue());
        } else {
            if (Objects.equals(onlineStoreType, CommonConst.SOURCE_PLATFORM_WPH)) {
                // 唯品会订单直接置为【一件代发订单】
                retailOrderDto.setType(CommonConst.ORDER_TYPE_DROPSHIPPING);
            } else {
                retailOrderDto.setType(CommonConst.ORDER_TYPE_ORDINARY);
            }
        }
    }

    /**
     * 补充字段判断
     */
    private static void supplementaryFiled(RetailOrderDto retailOrderDto, Map<String, Object> orderInfo) throws JsonProcessingException {
        if (Objects.equals(retailOrderDto.getOnlineStoreType(), CommonConst.SOURCE_PLATFORM_JD)) {
            // 京东
            List<HashMap<String, Object>> otherList = new ObjectMapper().readValue(new ObjectMapper().writeValueAsString(orderInfo.get(ORIGINAL_ORDER_J_Z_FIELD_SERVICE_OTHER_LIST)), new TypeReference<List<HashMap<String, Object>>>() {
            });
            if (CollectionUtils.isNotEmpty(otherList)) {
                for (HashMap<String, Object> objectHashMap : otherList) {
                    if (Objects.equals(MapUtils.getString(objectHashMap, ORIGINAL_ORDER_J_Z_FIELD_SERVICE_OTHER_LIST_TYPE, StringUtils.EMPTY), ORIGINAL_ORDER_J_Z_FIELD_SERVICE_OTHER_LIST_STORE)) {
                        String storeOrder = MapUtils.getString(objectHashMap, ORIGINAL_ORDER_J_Z_FIELD_SERVICE_OTHER_LIST_STORE_ORDER, StringUtils.EMPTY);
                        if (StringUtils.isNotBlank(storeOrder)) {
                            if (Objects.equals(storeOrder, JD_STORE_ORDER_CLOUD_WAREHOUSE)) {
                                // 京仓订单
                                retailOrderDto.setSupplementaryOrderType(JD_SUPPLEMENTARY_ORDER_TYPE_JING_WAREHOUSE);
                            } else if (Objects.equals(storeOrder, JD_STORE_ORDER_JING_WAREHOUSE)) {
                                // 云仓订单
                                retailOrderDto.setSupplementaryOrderType(JD_SUPPLEMENTARY_ORDER_TYPE_CLOUD_WAREHOUSE);
                            }
                            break;
                        }
                    }
                }
            }
            if (retailOrderDto.getSupplementaryOrderType() == null) {
                // 判断是否是SOP订单
                Integer businessType = MapUtils.getInteger(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_BUSINESS_TYPE, 0);
                if (!Objects.equals(businessType, JD_FIELD_VALUE_BUSINESS_TYPE_SOP)) {
                    retailOrderDto.setSupplementaryOrderType(JD_SUPPLEMENTARY_ORDER_TYPE_NOT_SOP);
                }
            }
        } else if (Objects.equals(retailOrderDto.getOnlineStoreType(), CommonConst.SOURCE_PLATFORM_WPH)) {
            // 唯品会
            HashMap<String, Object> shipping = getMapObject(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_SHIPPING);
            // 运单号
            retailOrderDto.setShippingId(MapUtils.getString(shipping, ORIGINAL_ORDER_J_Z_FIELD_SHIPPING_SHIPPING_ID, StringUtils.EMPTY));
            // 承运商
            retailOrderDto.setShippingName(MapUtils.getString(shipping, ORIGINAL_ORDER_J_Z_FIELD_SHIPPING_SHIPPING_NAME, StringUtils.EMPTY));
        } else if (Objects.equals(retailOrderDto.getOnlineStoreType(),CommonConst.SOURCE_PLATFORM_SHOPEE)) {
            HashMap<String, Object> shipping = getMapObject(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_SHIPPING);
            retailOrderDto.setPreDesignatedLogisticsCode(MapUtils.getString(shipping, ORIGINAL_ORDER_J_Z_FIELD_SHIPPING_SHIPPING_NAME, StringUtils.EMPTY));
        } else if (Objects.equals(retailOrderDto.getOnlineStoreType(), CommonConst.SOURCE_PLATFORM_DEWU)) {
            HashMap<String, Object> extendField = getMapObject(orderInfo, ORIGINAL_ORDER_J_Z_FIELD_EXTEND_FIELD);
            // 订单类型
            switch (MapUtils.getInteger(extendField, ORIGINAL_ORDER_J_Z_FIELD_EXTEND_FIELD_ORDER_TYPE)) {
                case 0: //普通现货
                    retailOrderDto.setOrderBusinessType(CommonConst.ORDER_BUSINESS_TYPE_0);// 普通现货订单
                    break;
                case 7: //极速现货
                case 8: //极速预售
                    retailOrderDto.setOrderBusinessType(CommonConst.ORDER_BUSINESS_TYPE_1);// 极速现货订单
                    break;
                case 26: //品牌直发
                case 39: //定金预售直发
                    retailOrderDto.setOrderBusinessType(CommonConst.ORDER_BUSINESS_TYPE_2);// 品牌直发订单
                    break;
                default:
                    break;
            }
            // 品牌直发履约类型
            retailOrderDto.setPerformanceType(MapUtils.getInteger(extendField, ORIGINAL_ORDER_J_Z_FIELD_EXTEND_FIELD_PERFORMANCE_TYPE, null));
            // 合并发货用户标识
            retailOrderDto.setMergeDeliveryIdentify(MapUtils.getString(extendField, ORIGINAL_ORDER_J_Z_FIELD_EXTEND_FIELD_MERGE_DELIVERY_IDENTIFY, StringUtils.EMPTY));
            // 是否可以合并发货
            retailOrderDto.setCanMergeDeliveryFlag(MapUtils.getBoolean(extendField, ORIGINAL_ORDER_J_Z_FIELD_EXTEND_FIELD_CAN_MERGE_DELIVERY_FLAG, null));
            // 订单退货到卖家信息
            retailOrderDto.setReturnToSeller(MapUtils.getString(extendField, ORIGINAL_ORDER_J_Z_FIELD_EXTEND_FIELD_RETURN_TO_SELLER, StringUtils.EMPTY));
        }
    }

    /**
     * 获取预先指定物流公司
     */
    private static String getPreDesignatedLogisticsCode(Map<String, Object> orderInfo) throws JsonProcessingException {
        List<HashMap<String, Object>> orderTagList = getMapList(orderInfo, PDD_ORIGINAL_ORDER_J_Z_FIELD_ORDER_TAG_LIST);
        if (CollectionUtils.isNotEmpty(orderTagList)) {
            for (HashMap<String, Object> map : orderTagList) {
                if (Objects.equals(MapUtils.getString(map, PDD_ORIGINAL_ORDER_J_Z_FIELD_ORDER_TAG_LIST_NAME, StringUtils.EMPTY), PDD_ORDER_TAG_LIST_HAS_SF_EXPRESS_SERVICE) && Objects.equals(MapUtils.getInteger(map, PDD_ORIGINAL_ORDER_J_Z_FIELD_ORDER_TAG_LIST_VALUE, 0), PDD_ORDER_TAG_LIST_VALUE_TRUE)) {
                    // 顺丰加价
                    return "SF";
                }
            }
        }
        return StringUtils.EMPTY;
    }

    /**
     * 平台交易状态
     *
     * <AUTHOR>
     */
    @Getter
    @ToString
    protected enum TransactionStatusEnum {
        /**
         * 等待买家付款
         */
        WAIT_BUYER_PAY("WAIT_BUYER_PAY", 1, "等待买家付款"),
        /**
         * 等待卖家发货
         */
        WAIT_SELLER_SEND_GOODS("WAIT_SELLER_SEND_GOODS", 2, "等待卖家发货"),
        /**
         * 卖家部分发货
         */
        SELLER_CONSIGNED_PART("SELLER_CONSIGNED_PART", 3, "卖家部分发货"),
        /**
         * 等待买家确认收货
         */
        WAIT_BUYER_CONFIRM_GOODS("WAIT_BUYER_CONFIRM_GOODS", 4, "等待买家确认收货"),
        /**
         * 买家已经申请退款，等待卖家同意
         */
        WAIT_SELLER_AGREE("WAIT_SELLER_AGREE", 5, "买家已经申请退款，等待卖家同意"),
        /**
         * 卖家已经同意退款，等待买家退货
         */
        WAIT_SELLER_CONFIRM_GOODS("WAIT_SELLER_CONFIRM_GOODS", 6, "卖家已经同意退款，等待买家退货"),
        /**
         * 交易完成
         */
        TRADE_FINISHED("TRADE_FINISHED", 8, "交易完成"),
        /**
         * 交易关闭
         */
        TRADE_CLOSED("TRADE_CLOSED", 9, "交易关闭"), TRADE_CLOSED_BY_TAOBAO("TRADE_CLOSED_BY_TAOBAO", 9, "交易关闭");

        private final String code;
        private final Integer value;
        private final String name;

        TransactionStatusEnum(String code, Integer value, String name) {
            this.code = code;
            this.value = value;
            this.name = name;
        }

        public static TransactionStatusEnum getTransactionStatusEnumByCode(String code) {
            if (StringUtils.isNotBlank(code)) {
                for (TransactionStatusEnum transactionStatusEnum : TransactionStatusEnum.values()) {
                    if (Objects.equals(transactionStatusEnum.getCode(), code)) {
                        return transactionStatusEnum;
                    }
                }
            }
            return null;
        }

    }

    /**
     * 订单来源 枚举
     */
    @ToString
    @Getter
    protected enum SaleTypeEnum {
        /**
         * 订单来源: fixed=直销
         */
        FIXED("fixed", 0, "直销"),
        /**
         * 订单来源: fenxiao=分销
         */
        DISTRIBUTION("fenxiao", 1, "分销"),

        ;

        private final String code;
        private final Integer value;
        private final String name;

        SaleTypeEnum(String code, Integer value, String name) {
            this.code = code;
            this.value = value;
            this.name = name;
        }

        public static SaleTypeEnum getSaleTypeEnumByCode(String code) {
            if (StringUtils.isNotBlank(code)) {
                for (SaleTypeEnum saleTypeEnum : SaleTypeEnum.values()) {
                    if (Objects.equals(saleTypeEnum.getCode(), code)) {
                        return saleTypeEnum;
                    }
                }
            }
            return null;
        }
    }

    /**
     * 交易类型 枚举
     */
    @ToString
    @Getter
    protected enum TransactionType {
        /**
         * 交易类型: step=定金预售
         */
        STEP("step", "定金预售"),

        ;

        private final String code;
        private final String name;

        TransactionType(String code, String name) {
            this.code = code;
            this.name = name;
        }
    }

    /**
     * 定金预售状态 枚举
     */
    @ToString
    @Getter
    protected enum StepTradeStatusEnum {
        /**
         * 定金预售状态: 定金未付尾款未付
         */
        UNPAID("FRONT_NOPAID_FINAL_NOPAID", 1),
        /**
         * 定金预售状态: 定金已付尾款未付
         */
        DEPOSIT_PAID("FRONT_PAID_FINAL_NOPAID", 2),
        /**
         * 定金预售状态: 定金和尾款都已付
         */
        FULL_PAID("FRONT_PAID_FINAL_PAID", 3),


        ;

        private final String code;
        private final Integer value;

        StepTradeStatusEnum(String code, Integer value) {
            this.code = code;
            this.value = value;
        }

        public static StepTradeStatusEnum getStepTradeStatusEnumByCode(String code) {
            if (StringUtils.isNotBlank(code)) {
                for (StepTradeStatusEnum stepTradeStatusEnum : StepTradeStatusEnum.values()) {
                    if (Objects.equals(stepTradeStatusEnum.getCode(), code)) {
                        return stepTradeStatusEnum;
                    }
                }
            }
            return null;
        }
    }
}
