package cn.shopex.hobbits.ms.harbor.api.inner.biz;

import cn.shopex.hobbits.ms.harbor.api.common.domain.CommonResponseGeneric;
import cn.shopex.hobbits.ms.harbor.api.inner.domain.OrderTraceRecordDto;
import cn.shopex.hobbits.ms.library.common.MyObjectMapper;
import cn.shopex.hobbits.ms.library.redundant.base.domain.OnlineStoreDto;
import cn.shopex.hobbits.ms.library.redundant.base.service.BaseRedundantService;
import cn.shopex.hobbits.ms.library.redundant.configuration.domain.RedundantShopexAuthorization;
import cn.shopex.hobbits.ms.library.redundant.configuration.service.ApplicationRedundantService;
import cn.shopex.hobbits.neb.MatrixClient;
import cn.shopex.hobbits.neb.domain.MatrixResponse;
import cn.shopex.hobbits.neb.domain.OrderTraceRecordParam;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Slf4j
@Component
@RequiredArgsConstructor
public class OrderTraceRecordBiz {
    private final ApplicationRedundantService applicationRedundantService;
    private final BaseRedundantService baseRedundantService;
    private final MatrixClient matrixClient;
    private final MyObjectMapper myObjectMapper;

    public MatrixResponse traceRecordNotify(List<OrderTraceRecordDto> orderTraceRecordDtoList) {

        OrderTraceRecordParam orderTraceRecordParam = new OrderTraceRecordParam();
        RedundantShopexAuthorization authorization = this.applicationRedundantService.selectShopexAuthorizationToken();
        //系统参数
        orderTraceRecordParam.setFromNodeId(authorization.getNode());
        orderTraceRecordParam.setCertiId(authorization.getCertificate());
        orderTraceRecordParam.setToken(authorization.getToken());
        OnlineStoreDto onlineStoreDto = baseRedundantService.selectOnlineStore(orderTraceRecordDtoList.get(0).getStoreCode());
        orderTraceRecordParam.setToNodeId(onlineStoreDto.getSystemNode());
        List<String> tidList = Lists.newArrayList();
        for (OrderTraceRecordDto orderTraceRecordDto : orderTraceRecordDtoList) {
            orderTraceRecordDto.setStoreCode(null);
            tidList.add(orderTraceRecordDto.getOrderId());
        }
        Map<String, String> uploadParam = Maps.newHashMap();
        uploadParam.put("trace_list", myObjectMapper.writeValueAsString(orderTraceRecordDtoList));
        uploadParam.put("node_id", onlineStoreDto.getSystemNode());
        orderTraceRecordParam.setTid(String.join(",", tidList));
        orderTraceRecordParam.setUploadParams(myObjectMapper.writeValueAsString(uploadParam));
        return matrixClient.orderTraceRecord(orderTraceRecordParam);
    }


}
