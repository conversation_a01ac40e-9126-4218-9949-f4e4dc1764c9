package cn.shopex.hobbits.ms.harbor.common.order.constant;

/**
 * 原始订单字段常量
 *
 * <AUTHOR>
 */
public interface OriginalOrderFieldConst {

    /**
     * 额外追加自定义字段: 店铺编码
     */
    String EXTRA_APPEND_FIELD_STORE_CODE = "extra_append_field_store_code";
    /**
     * 额外追加自定义字段: 来源平台 京东、淘宝、抖音
     */
    String EXTRA_APPEND_FIELD_SOURCE_PLATFORM = "extra_append_field_source_platform";
    /**
     * 额外追加自定义字段: 订单下载时间
     */
    String EXTRA_APPEND_FIELD_ORDER_DOWN_TIME = "extra_append_field_order_down_time";
    /**
     * 额外追加自定义字段: 店铺业务模式
     */
    String EXTRA_APPEND_FIELD_BUSINESS_TYPE = "extra_append_field_business_type";
    /**
     * 额外追加自定义字段: 订单来源中文名称
     */
    String EXTRA_APPEND_FIELD_T_TYPE_STR = "extra_append_field_t_type_str";
    /**
     * 额外追加自定义字段: 交易类型中文名称
     */
    String EXTRA_APPEND_FIELD_TRADE_TYPE_STR = "extra_append_field_trade_type_str";
    /**
     * 额外追加自定义字段: 是否存在保价
     */
    String EXTRA_APPEND_FIELD_IS_COST_PROTECT = "extra_append_field_is_cost_protect";
    /**
     * 额外追加自定义字段: 订单商品总数量
     */
    String EXTRA_APPEND_FIELD_ORDER_GOODS_QUANTITY = "extra_append_field_order_goods_quantity";
    /**
     * 额外追加自定义字段: 平台交易状态中文名称
     */
    String EXTRA_APPEND_FIELD_TRADING_STATUS_STR = "extra_append_field_trading_status_str";
    /**
     * 额外追加自定义字段: 付款状态中文名称
     */
    String EXTRA_APPEND_FIELD_PAYMENT_STATUS_STR = "extra_append_field_payment_status_str";
    /**
     * 额外追加自定义字段: 下单时间字符串
     */
    String EXTRA_APPEND_FIELD_CREATE_TIME_STR = "extra_append_field_create_time_str";
    /**
     * 额外追加自定义字段: 平台最后更新时间字符串
     */
    String EXTRA_APPEND_FIELD_MODIFIED_STR = "extra_append_field_last_modify_str";

    /**
     * 原始订单json字段: 系统字段: 系统节点
     */
    String ORIGINAL_ORDER_J_Z_SYSTEM_NODE = "from_node_id";

    /**
     * 原始订单json字段: 平台原始交易单号
     */
    String ORIGINAL_ORDER_J_Z_FIELD_TID = "order_bn";
    /**
     * 原始订单json字段: 平台最后更新时间
     */
    String ORIGINAL_ORDER_J_Z_FIELD_MODIFIED = "lastmodify";
    /**
     * 原始订单json字段: 订单来源 fixed=直销；fenxiao=分销（固定值）
     */
    String ORIGINAL_ORDER_J_Z_FIELD_T_TYPE = "t_type";
    /**
     * 原始订单json字段: 订单总金额
     */
    String ORIGINAL_ORDER_J_Z_FIELD_TOTAL_TRADE_FEE = "total_amount";
    /**
     * 原始订单json字段: 平台交易状态
     */
    String ORIGINAL_ORDER_J_Z_FIELD_STATUS = "source_status";
    /**
     * 原始订单json字段: 订单已支付金额
     */
    String ORIGINAL_ORDER_J_Z_FIELD_PAYED_FEE = "payed";
    /**
     * 原始订单json字段: 交易标题，以店铺名作为此标题的值
     */
    String ORIGINAL_ORDER_J_Z_FIELD_TITLE = "title";
    /**
     * 原始订单json字段: 交易类型
     */
    String ORIGINAL_ORDER_J_Z_FIELD_TRADE_TYPE = "trade_type";
    /**
     * 原始订单json字段: 定金预售状态
     */
    String ORIGINAL_ORDER_J_Z_FIELD_STEP_TRADE_STATUS = "step_trade_status";
    /**
     * 原始订单json字段: 货币
     */
    String ORIGINAL_ORDER_J_Z_FIELD_CURRENCY = "currency";
    /**
     * 原始订单json字段: 订单结束时间、签收时间
     */
    String ORIGINAL_ORDER_J_Z_FIELD_END_TIME = "end_time";
    /**
     * 原始订单json字段: 下单时间
     */
    String ORIGINAL_ORDER_J_Z_FIELD_CREATE_TIME = "createtime";
    /**
     * 原始订单json字段: 平台旗标
     */
    String ORIGINAL_ORDER_J_Z_FIELD_MARK_TYPE = "mark_type";
    /**
     * 原始订单json字段: 卖家备注
     */
    String ORIGINAL_ORDER_J_Z_FIELD_MARK_TEXT = "mark_text";
    /**
     * 原始订单json字段: 买家备注
     */
    String ORIGINAL_ORDER_J_Z_FIELD_BUYER_MEMO = "custom_mark";
    /**
     * 原始订单json字段: 商品总额
     */
    String ORIGINAL_ORDER_J_Z_FIELD_COST_ITEM = "cost_item";
    /**
     * 原始订单json字段: 订单优惠
     */
    String ORIGINAL_ORDER_J_Z_FIELD_PMT_ORDER = "pmt_order";
    /**
     * 原始订单json字段: 订单手工调价
     */
    String ORIGINAL_ORDER_J_Z_FIELD_PMT_GOODS = "pmt_goods";
    /**
     * 原始订单json字段: 是否开票
     */
    String ORIGINAL_ORDER_J_Z_FIELD_IS_TAX = "is_tax";
    /**
     * 原始订单json字段: 注册地址
     */
    String ORIGINAL_ORDER_J_Z_FIELD_INVOICE_ADDRESS = "invoice_address";
    /**
     * 原始订单json字段: 银行账户（加密）
     */
    String ORIGINAL_ORDER_J_Z_FIELD_INVOICE_BANK_ACCOUNT = "invoice_bank_account";
    /**
     * 原始订单json字段: 开户银行
     */
    String ORIGINAL_ORDER_J_Z_FIELD_INVOICE_BANK_NAME = "invoice_bank_name";
    /**
     * 原始订单json字段: 注册电话
     */
    String ORIGINAL_ORDER_J_Z_FIELD_INVOICE_PHONE = "invoice_phone";

    /**
     * 原始订单json字段: 预估总重量
     */
    String ORIGINAL_ORDER_J_Z_FIELD_WEIGHT = "weight";
    /**
     * 原始订单json字段: 是否平台自发
     */
    String ORIGINAL_ORDER_J_Z_FIELD_IS_SH_SHIP = "is_sh_ship";
    /**
     * 原始订单json字段: 是否预售订单
     */
    String ORIGINAL_ORDER_J_Z_FIELD_IS_YU_SHOU = "is_yushou";
    /**
     * 原始订单json字段: 京东订单类型
     */
    String ORIGINAL_ORDER_J_Z_FIELD_BUSINESS_TYPE = "business_type";
    /**
     * 原始订单json字段: 预先指定配送方式
     */
    String ORIGINAL_ORDER_J_Z_FIELD_PRE_DESIGNATED_STORE_DELIVERY = "pre_designated_store_delivery";
    /**
     * 原始订单json字段: 预先指定门店
     */
    String ORIGINAL_ORDER_J_Z_FIELD_PRE_DESIGNATED_STORE_CODE = "PRE_DESIGNATED_STORE_CODE";
    /**
     * 原始订单json字段: 是否风控订单
     */
    String ORIGINAL_ORDER_J_Z_FIELD_IS_RISK = "is_risk";
    /**
     * (拼多多)原始订单json字段: 订单标签列表
     */
    String PDD_ORIGINAL_ORDER_J_Z_FIELD_ORDER_TAG_LIST = "order_tag_list";
    /**
     * (拼多多)原始订单json字段: 订单其他扩展字段-订单标签列表-name
     */
    String PDD_ORIGINAL_ORDER_J_Z_FIELD_ORDER_TAG_LIST_NAME = "name";
    /**
     * (拼多多)原始订单json字段: 订单其他扩展字段-订单标签列表-value
     */
    String PDD_ORIGINAL_ORDER_J_Z_FIELD_ORDER_TAG_LIST_VALUE = "value";

    /**
     * 原始订单json字段: 不重要的字段，但是要转换成json
     */
    String ORIGINAL_ORDER_J_Z_FIELD_YFX_INFO = "yfx_info";
    String ORIGINAL_ORDER_J_Z_FIELD_O2O_INFO = "o2o_info";
    String ORIGINAL_ORDER_J_Z_FIELD_SERVICE_ORDER_OBJECTS = "service_order_objects";
    String ORIGINAL_ORDER_J_Z_FIELD_PAY_INFO = "payinfo";
    String ORIGINAL_ORDER_J_Z_FIELD_SELF_FETCH_INFO = "self_fetch_info";
    String ORIGINAL_ORDER_J_Z_FIELD_PAYMENT_DETAIL = "payment_detail";

    /***************************other_list****************************/
    String ORIGINAL_ORDER_J_Z_FIELD_SERVICE_OTHER_LIST = "other_list";
    /**
     * 原始订单json字段: other_list-type
     */
    String ORIGINAL_ORDER_J_Z_FIELD_SERVICE_OTHER_LIST_TYPE = "type";
    /**
     * 原始订单json字段: other_list-store
     */
    String ORIGINAL_ORDER_J_Z_FIELD_SERVICE_OTHER_LIST_STORE = "store";
    /**
     * 原始订单json字段: other_list-store_order
     */
    String ORIGINAL_ORDER_J_Z_FIELD_SERVICE_OTHER_LIST_STORE_ORDER = "store_order";

    /***************************收件人信息****************************/
    String ORIGINAL_ORDER_J_Z_FIELD_CONSIGNEE = "consignee";
    /**
     * 原始订单json字段: 收件人信息-收件人省份
     */
    String ORIGINAL_ORDER_J_Z_FIELD_CONSIGNEE_AREA_STATE = "area_state";
    /**
     * 原始订单json字段: 收件人信息-收件人城市
     */
    String ORIGINAL_ORDER_J_Z_FIELD_CONSIGNEE_AREA_CITY = "area_city";
    /**
     * 原始订单json字段: 收件人信息-收件人区
     */
    String ORIGINAL_ORDER_J_Z_FIELD_CONSIGNEE_AREA_DISTRICT = "area_district";
    /**
     * 原始订单json字段: 收件人信息-收件人街道
     */
    String ORIGINAL_ORDER_J_Z_FIELD_CONSIGNEE_AREA_STREET = "area_street";
    /**
     * 原始订单json字段: 收件人信息-详细地址
     */
    String ORIGINAL_ORDER_J_Z_FIELD_CONSIGNEE_ADDR = "addr";
    /**
     * 原始订单json字段: 收件人信息-邮编
     */
    String ORIGINAL_ORDER_J_Z_FIELD_CONSIGNEE_ZIP = "zip";
    /**
     * 原始订单json字段: 收件人信息-收件人姓名
     */
    String ORIGINAL_ORDER_J_Z_FIELD_CONSIGNEE_CONSIGNEE_NAME = "name";
    /**
     * 原始订单json字段: 收件人信息-收件人手机号
     */
    String ORIGINAL_ORDER_J_Z_FIELD_CONSIGNEE_CONSIGNEE_MOBILE = "mobile";
    /**
     * 原始订单json字段: 收件人信息-收件人电话
     */
    String ORIGINAL_ORDER_J_Z_FIELD_CONSIGNEE_CONSIGNEE_TELEPHONE = "telephone";

    /***************************订单其他扩展字段****************************/
    String ORIGINAL_ORDER_J_Z_FIELD_EXTEND_FIELD = "extend_field";
    /**
     * 原始订单json字段: 购物金
     */
    String ORIGINAL_ORDER_J_Z_FIELD_EXTEND_FIELD_EXPAND_CARD_EXPAND_PRICE_USED = "expand_card_expand_price_used";
    /**
     * (抖音)原始订单json字段: 子订单信息-赠品信息
     */
    String DY_ORIGINAL_ORDER_J_Z_FIELD_EXTEND_FIELD_IS_FREE_GIFT = "is_free_gift";
    /**
     * (抖音)原始订单json字段: 子订单信息-赠品信息绑定的主品sku单单号
     */
    String DY_ORIGINAL_ORDER_J_Z_FIELD_EXTEND_FIELD_GIFT_MIDS = "gift_mids";
    /**
     * (抖音)原始订单json字段: 子订单信息-组合商品信息
     */
    String DY_ORIGINAL_ORDER_J_Z_FIELD_EXTEND_FIELD_BUNDLE_SKU_INFO = "bundle_sku_info";
    /**
     * (抖音)原始订单json字段: 子订单信息-组合商品子SKU编码
     */
    String DY_ORIGINAL_ORDER_J_Z_FIELD_EXTEND_FIELD_BUNDLE_SKU_INFO_CODE = "code";
    /**
     * (抖音)原始订单json字段: 子订单信息-组合商品子SKU数量
     */
    String DY_ORIGINAL_ORDER_J_Z_FIELD_EXTEND_FIELD_BUNDLE_SKU_INFO_ITEM_NUM = "item_num";
    /**
     * (抖音)原始订单json字段: 订单其他扩展字段-store_info
     */
    String DY_ORIGINAL_ORDER_J_Z_FIELD_EXTEND_FIELD_STORE_INFO = "store_info";
    /**
     * (抖音)原始订单json字段: 订单其他扩展字段-store_info-inventory_list
     */
    String DY_ORIGINAL_ORDER_J_Z_FIELD_EXTEND_FIELD_STORE_INFO_INVENTORY_LIST = "inventory_list";
    /**
     * (抖音)原始订单json字段: 订单其他扩展字段-store_info-inventory_list-指定仓库
     */
    String DY_ORIGINAL_ORDER_J_Z_FIELD_EXTEND_FIELD_STORE_INFO_INVENTORY_LIST_OUT_WAREHOUSE_ID = "out_warehouse_id";
    /**
     * 子单扩展信息列表
     */
    String ORIGINAL_ORDER_J_Z_FIELD_EXTEND_FIELD_OID_INFO_LIST = "oid_info_list";
    /**
     * 子单扩展信息: 子订单ID
     */
    String ORIGINAL_ORDER_J_Z_FIELD_EXTEND_FIELD_OID_INFO_OID = "oid";
    /**
     * 子单扩展信息: 平台分销单编号
     */
    String ORIGINAL_ORDER_J_Z_FIELD_EXTEND_FIELD_OID_INFO_PO_NO = "po_no";
    /**
     * 子单扩展信息: 达人信息
     */
    String ORIGINAL_ORDER_J_Z_FIELD_EXTEND_FIELD_OID_INFO_AUTHOR_INFO = "author_info";
    String ORIGINAL_ORDER_J_Z_FIELD_EXTEND_FIELD_OID_INFO_AUTHOR_ID = "author_id";
    String ORIGINAL_ORDER_J_Z_FIELD_EXTEND_FIELD_OID_INFO_AUTHOR_NAME = "author_name";
    /**
     * (得物)原始订单json字段: 订单类型
     */
    String ORIGINAL_ORDER_J_Z_FIELD_EXTEND_FIELD_ORDER_TYPE = "order_type";
    /**
     * (得物)原始订单json字段: 订单类型
     */
    String ORIGINAL_ORDER_J_Z_FIELD_EXTEND_FIELD_PERFORMANCE_TYPE = "performance_type";
    /**
     * (得物)原始订单json字段: 订单类型
     */
    String ORIGINAL_ORDER_J_Z_FIELD_EXTEND_FIELD_MERGE_DELIVERY_IDENTIFY = "merge_delivery_identify";
    /**
     * (得物)原始订单json字段: 订单类型
     */
    String ORIGINAL_ORDER_J_Z_FIELD_EXTEND_FIELD_CAN_MERGE_DELIVERY_FLAG = "can_merge_delivery_flag";
    /**
     * (得物)原始订单json字段: 订单退货到卖家信息
     */
    String ORIGINAL_ORDER_J_Z_FIELD_EXTEND_FIELD_RETURN_TO_SELLER = "return_to_seller";
    /***************************index_field****************************/
    String ORIGINAL_ORDER_J_Z_FIELD_INDEX_FIELD = "index_field";
    /**
     * 原始订单json字段: 发票类型
     */
    String ORIGINAL_ORDER_J_Z_FIELD_INVOICE_KIND = "invoice_kind";
    /**
     * 原始订单json字段: index_field-发票纳税人识别号
     */
    String ORIGINAL_ORDER_J_Z_FIELD_INDEX_FIELD_PAYER_REGISTER_NO_INDEX = "payer_register_no_index";
    /**
     * 原始订单json字段: index_field-收票人地址（加密）
     */
    String ORIGINAL_ORDER_J_Z_FIELD_INDEX_FIELD_INVOICE_BUYER_ADDRESS_INDEX = "invoice_buyer_address_index";
    /**
     * 原始订单json字段: index_field-收票人电话（加密）
     */
    String ORIGINAL_ORDER_J_Z_FIELD_INDEX_FIELD_INVOICE_BUYER_PHONE_INDEX = "invoice_buyer_phone_index";
    /**
     * 原始订单json字段: index_field-收票人姓名（加密）
     */
    String ORIGINAL_ORDER_J_Z_FIELD_INDEX_FIELD_RECEIVER_NAME_INDEX = "receiver_name_index";
    /**
     * 原始订单json字段: 发票抬头 个人/公司名称（加密）
     */
    String ORIGINAL_ORDER_J_Z_FIELD_TAX_TITLE = "invoiceTitle";
    /**
     * 原始订单json字段: 电子发票联系人邮箱（加密）
     */
    String ORIGINAL_ORDER_J_Z_FIELD_INVOICE_CONSIGNEE_EMAIL = "invoiceConsigneeEmail";
    /**
     * 原始订单json字段: 电子发票联系人手机号（加密）
     */
    String ORIGINAL_ORDER_J_Z_FIELD_INVOICE_CONSIGNEE_PHONE = "invoiceConsigneePhone";

    /***************************会员信息****************************/
    String ORIGINAL_ORDER_J_Z_FIELD_MEMBER_INFO = "member_info";
    /**
     * 原始订单json字段: 会员信息-会员ID
     */
    String ORIGINAL_ORDER_J_Z_FIELD_MEMBER_INFO_UNAME = "uname";
    /**
     * 原始订单json字段: 会员信息-买家OUID
     */
    String ORIGINAL_ORDER_J_Z_FIELD_MEMBER_INFO_BUYER_OPEN_UID = "buyer_open_uid";

    /***************************支付信息****************************/
    String ORIGINAL_ORDER_J_Z_FIELD_PAYMENT = "payments";
    /**
     * 原始订单json字段: 支付信息-支付方式ID
     */
    String ORIGINAL_ORDER_J_Z_FIELD_PAYMENT_PAY_BN = "pay_bn";
    /**
     * 原始订单json字段: 支付信息-支付方式名
     */
    String ORIGINAL_ORDER_J_Z_FIELD_PAYMENT_PAYMETHOD = "paymethod";
    /**
     * 原始订单json字段: 支付信息-支付时间
     */
    String ORIGINAL_ORDER_J_Z_FIELD_PAYMENT_PAY_TIME = "pay_time";
    /**
     * 原始订单json字段: 支付信息-平台支付单号
     */
    String ORIGINAL_ORDER_J_Z_FIELD_PAYMENT_TRADE_NO = "trade_no";
    /**
     * 原始订单json字段: 支付信息-支付金额
     */
    String ORIGINAL_ORDER_J_Z_FIELD_PAYMENT_MONEY = "money";

    /***************************子订单信息****************************/
    String ORIGINAL_ORDER_J_Z_FIELD_ORDER_OBJECTS = "order_objects";
    /**
     * 原始订单json字段: 子订单信息-货品编码
     */
    String ORIGINAL_ORDER_J_Z_FIELD_ORDER_OBJECTS_BN = "bn";
    /**
     * 原始订单json字段: 子订单信息-平台商品id
     */
    String ORIGINAL_ORDER_J_Z_FIELD_ORDER_OBJECTS_SHOP_GOODS_ID = "shop_goods_id";

    /**
     * 原始订单json字段: 子订单信息-平台商品名称
     */
    String ORIGINAL_ORDER_J_Z_FIELD_ORDER_OBJECTS_SHOP_GOODS_NAME = "name";
    /**
     * 原始订单json字段: 子订单信息-平台skuid
     */
    String ORIGINAL_ORDER_J_Z_FIELD_ORDER_OBJECTS_SHOP_PRODUCT_ID = "shop_product_id";
    /**
     * 原始订单json字段: 子订单信息-销售价
     */
    String ORIGINAL_ORDER_J_Z_FIELD_ORDER_OBJECTS_PRICE = "price";
    /**
     * 原始订单json字段: 子订单信息-手工调价
     */
    String ORIGINAL_ORDER_J_Z_FIELD_ORDER_OBJECTS_ADJUST_FEE = "adjust_fee";
    /**
     * 原始订单json字段: 子订单信息-优惠分摊
     */
    String ORIGINAL_ORDER_J_Z_FIELD_ORDER_OBJECTS_PART_MJZ_DISCOUNT = "part_mjz_discount";
    /**
     * 原始订单json字段: 子订单信息-成交金额
     */
    String ORIGINAL_ORDER_J_Z_FIELD_ORDER_OBJECTS_DIVIDE_ORDER_FEE = "divide_order_fee";
    /**
     * 原始订单json字段: 子订单信息-购买数量
     */
    String ORIGINAL_ORDER_J_Z_FIELD_ORDER_OBJECTS_QUANTITY = "quantity";
    /**
     * 原始订单json字段: 子订单信息-平台子单号
     */
    String ORIGINAL_ORDER_J_Z_FIELD_ORDER_OBJECTS_OID = "oid";
    /**
     * 原始订单json字段: 子订单信息-平台子单状态
     */
    String ORIGINAL_ORDER_J_Z_FIELD_ORDER_OBJECTS_SOURCE_STATUS = "source_status";
    /**
     * 原始订单json字段: 子订单信息-自订单属性
     */
    String ORIGINAL_ORDER_J_Z_FIELD_ORDER_OBJECTS_ORDER_ATTR = "order_attr";
    /**
     * 原始订单json字段: 子订单信息-预计发货时间
     */
    String ORIGINAL_ORDER_J_Z_FIELD_ORDER_OBJECTS_ESTIMATE_CON_TIME = "estimate_con_time";
    /**
     * 原始订单json字段: 子订单信息-平台分销单编号
     */
    String ORIGINAL_ORDER_J_Z_FIELD_ORDER_OBJECTS_PO_NO = "po_no";
    /**
     * 原始订单json字段: 子订单信息-子商品信息
     */
    String ORIGINAL_ORDER_J_Z_FIELD_ORDER_OBJECTS_ORDER_ITEMS = "order_items";
    /**
     * 原始订单json字段: 子订单信息-子商品信息-优惠金额
     */
    String ORIGINAL_ORDER_J_Z_FIELD_ORDER_OBJECTS_ORDER_ITEMS_PMT_PRICE = "pmt_price";
    /**
     * 原始订单json字段: 子订单信息-子商品信息-购物金优惠
     */
    String ORIGINAL_ORDER_J_Z_FIELD_ORDER_OBJECTS_ORDER_ITEMS_EXPAND_CARD_EXPAND_PRICE_USED_SUBORDER = "expand_card_expand_price_used_suborder";
    /**
     * 原始订单json字段: 子订单信息-子商品信息-平台商品ID
     */
    String ORIGINAL_ORDER_J_Z_FIELD_ORDER_OBJECTS_ORDER_ITEMS_SHOP_PRODUCT_ID = "shop_product_id";

    /**
     * 原始订单json字段: 子订单信息-子商品信息-属性字符串
     */
    String ORIGINAL_ORDER_J_Z_FIELD_ORDER_OBJECTS_ORDER_ITEMS_PROPERTIES_STR = "original_str";
    String ORIGINAL_ORDER_J_Z_FIELD_ORDER_OBJECTS_ORDER_ITEMS_EXTEND_ITEM_LIST = "extend_item_list";

    /***************************配送信息****************************/
    String ORIGINAL_ORDER_J_Z_FIELD_SHIPPING = "shipping";
    /**
     * 原始订单json字段: 配送信息-配送费用
     */
    String ORIGINAL_ORDER_J_Z_FIELD_SHIPPING_COST_SHIPPING = "cost_shipping";
    /**
     * 原始订单json字段: 配送信息-保价费用
     */
    String ORIGINAL_ORDER_J_Z_FIELD_SHIPPING_COST_PROTECT = "cost_protect";
    /**
     * 原始订单json字段: 配送信息-货款方式 是否货到付款 is_cod=true为货到付款
     */
    String ORIGINAL_ORDER_J_Z_FIELD_SHIPPING_IS_COD = "is_cod";
    /**
     * 原始订单json字段: 配送信息-运单号
     */
    String ORIGINAL_ORDER_J_Z_FIELD_SHIPPING_SHIPPING_ID = "shipping_id";
    /**
     * 原始订单json字段: 配送信息-承运商
     */
    String ORIGINAL_ORDER_J_Z_FIELD_SHIPPING_SHIPPING_NAME = "shipping_name";

    /***************************优惠信息****************************/
    String ORIGINAL_ORDER_J_Z_FIELD_PMT_DETAIL = "pmt_detail";
    /**
     * 原始订单json字段: 优惠信息-优惠券id
     */
    String ORIGINAL_ORDER_J_Z_FIELD_PMT_DETAIL_PROMOTION_ID = "promotion_id";
    /**
     * 原始订单json字段: 优惠信息-优惠金额
     */
    String ORIGINAL_ORDER_J_Z_FIELD_PMT_DETAIL_PMT_AMOUNT = "pmt_amount";
    /**
     * 原始订单json字段: 优惠信息-优惠名称
     */
    String ORIGINAL_ORDER_J_Z_FIELD_PMT_DETAIL_PMT_NAME = "pmt_describe";
    /**
     * 原始订单json字段: 优惠信息-优惠描述
     */
    String ORIGINAL_ORDER_J_Z_FIELD_PMT_DETAIL_PMT_DESCRIBE = "pmt_describe";
    /**
     * 原始订单json字段: 优惠信息-子单号
     */
    String ORIGINAL_ORDER_J_Z_FIELD_PMT_DETAIL_PMT_ID = "pmt_id";

    /***************************推销员信息****************************/
    String ORIGINAL_ORDER_J_Z_FIELD_PROMOTION_INFO = "promotion_info";
    String ORIGINAL_ORDER_J_Z_FIELD_PROMOTION_INFO_CHANNEL_CODE = "channel_code";
    String ORIGINAL_ORDER_J_Z_FIELD_PROMOTION_INFO_SALESMAN_CODE = "salesman_code";

    /**
     * 微信小店:是否为送礼订单
     */
    String ORIGINAL_ORDER_J_Z_FIELD_IS_PRESENT = "is_present";
}
