package cn.shopex.hobbits.ms.harbor.common.domain.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class RetailOrderDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("平台订单号")
    private String platformOrderNumber;

    @ApiModelProperty("下单时间")
    private Long orderTime;

    @ApiModelProperty("签收时间")
    private String signingTimeStr;

    @ApiModelProperty("签收时间")
    private Long signingTime;

    @ApiModelProperty("平台最后更新时间")
    private Long lastModifiedTime;

    @ApiModelProperty("下载时间")
    private Long downloadTime;

    @ApiModelProperty("来源店铺编码")
    private String onlineStoreCode;

    @ApiModelProperty("店铺类型")
    private Integer onlineStoreType;

    @ApiModelProperty("业务模式")
    private String businessModel;

    @ApiModelProperty("销售模式")
    private Integer salesModel;

    @ApiModelProperty("平台旗标")
    private String flagId;

    @ApiModelProperty("卖家备注")
    private String sellerMemo;

    @ApiModelProperty("买家备注")
    private String buyerMemo;

    @ApiModelProperty("订单类型")
    private Integer type;

    @ApiModelProperty("订单业务类型")
    private Integer orderBusinessType;

    @ApiModelProperty("预售类型")
    private Integer preSaleType;

    @ApiModelProperty("预售状态")
    private Integer preSaleStatus;

    @ApiModelProperty("商品总额")
    private BigDecimal goodsTotalFee;

    @ApiModelProperty("配送费用")
    private BigDecimal shippingFee;

    @ApiModelProperty("订单总金额")
    private BigDecimal orderTotalFee;

    @ApiModelProperty("订单优惠")
    private BigDecimal orderDiscountFee;

    @ApiModelProperty("订单手工调价")
    private BigDecimal orderManualDiscountFee;

    @ApiModelProperty("已付金额")
    private BigDecimal payedFee;

    @ApiModelProperty("平台实付金额 = 订单已支付金额")
    private BigDecimal platformPayedFee;

    @ApiModelProperty("保价费用")
    private BigDecimal protectFee;

    @ApiModelProperty("付款状态")
    private Integer paymentStatus;

    @ApiModelProperty("平台交易状态")
    private Integer tradingStatus;

    @ApiModelProperty("货款方式")
    private Integer paymentType;

    @ApiModelProperty("是否开票")
    private Integer isInvoice;

    @ApiModelProperty("预估总重量")
    private BigDecimal estimatedTotalWeight;

    @ApiModelProperty("买家信息")
    private BuyerDto baseBuyerDto;

    @ApiModelProperty("支付信息")
    private List<PaymentDto> basePaymentDtoList;

    @ApiModelProperty("商品信息")
    private List<GoodsDto> baseGoodsDtoList;

    @ApiModelProperty("优惠信息")
    private List<DiscountDto> baseDiscountDtoList;

    @ApiModelProperty("发票信息")
    private InvoiceDto baseInvoice;

    @ApiModelProperty("会员id")
    private String memberId;

    @ApiModelProperty("买家ouid")
    private String buyerOpenUid;

    @ApiModelProperty("操作人")
    private String operator;

    @ApiModelProperty(value = "原始平台交易状态")
    private String sourceStatus;

    @ApiModelProperty(value = "交易标题")
    private String transactionTitle;

    @ApiModelProperty(value = "交易类型")
    private String transactionType;

    @ApiModelProperty(value = "结束时间")
    private String completionTime;

    @ApiModelProperty(value = "是否平台自发")
    private Boolean isShShip;

    @ApiModelProperty(value = "预先指定物流")
    private String preDesignatedLogisticsCode;

    @ApiModelProperty(value = "是否风控订单")
    private Boolean isRisk;

    @ApiModelProperty(value = "是否物流中转订单")
    private Boolean isLogisticsTransit = false;

    @ApiModelProperty("预先指定门店")
    private String preDesignatedStoreCode;

    @ApiModelProperty(value = "预先指定门店的配送方式")
    private Integer preDesignatedStoreDelivery;

    @ApiModelProperty(value = "推广渠道编码")
    private String promotionChannelCode = StringUtils.EMPTY;

    @ApiModelProperty(value = "推广渠道名称")
    private String promotionChannelName = StringUtils.EMPTY;

    @ApiModelProperty(value = "销售员编码")
    private String salesmanCode = StringUtils.EMPTY;

    @ApiModelProperty(value = "销售员姓名")
    private String salesmanName = StringUtils.EMPTY;

    @ApiModelProperty(value = "补充订单类型字段, 用于check")
    private Integer supplementaryOrderType;

    @ApiModelProperty(value = "是否只创建零售订单 （POS方案二 会传true，其他情况均为false）")
    private Boolean isOnlyCreateRetailOrder;
    
    @ApiModelProperty("运单号")
    private String shippingId;

    @ApiModelProperty("承运商编码")
    private String shippingName;

    @ApiModelProperty(value = "父平台订单号")
    private String parentPlatformOrderNumber = StringUtils.EMPTY;

    @ApiModelProperty(value = "根平台订单号")
    private String rootPlatformOrderNumber = StringUtils.EMPTY;

    @ApiModelProperty(value = "(得物)品牌直发履约类型")
    private Integer performanceType;

    @ApiModelProperty(value = "(得物)合并发货用户标识")
    private String mergeDeliveryIdentify;

    @ApiModelProperty(value = "(得物)是否可以合并发货")
    private Boolean canMergeDeliveryFlag;

    @ApiModelProperty(value = "(得物)订单退货到卖家信息")
    private String returnToSeller;

    @ApiModelProperty(value = "是否送礼订单")
    private Boolean isPresent;
}
