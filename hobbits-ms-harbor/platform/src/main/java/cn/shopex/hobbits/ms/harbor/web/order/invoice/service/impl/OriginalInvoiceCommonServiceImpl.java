package cn.shopex.hobbits.ms.harbor.web.order.invoice.service.impl;

import cn.shopex.hobbits.ms.harbor.callback.order.domain.OnlineStoreDto;
import cn.shopex.hobbits.ms.harbor.callback.order.handler.AlibabaOriginalInvoiceTransformer;
import cn.shopex.hobbits.ms.harbor.callback.order.handler.DouYinOriginalInvoiceTransformer;
import cn.shopex.hobbits.ms.harbor.callback.order.handler.OriginalInvoiceTransformer;
import cn.shopex.hobbits.ms.harbor.callback.order.handler.PddOriginalInvoiceTransformer;
import cn.shopex.hobbits.ms.harbor.common.CommonConst;
import cn.shopex.hobbits.ms.harbor.common.MessageConst;
import cn.shopex.hobbits.ms.harbor.common.MessageQueueConst;
import cn.shopex.hobbits.ms.harbor.common.domain.order.ConversionOriginalInvoiceResultDto;
import cn.shopex.hobbits.ms.harbor.common.domain.order.InvoiceApplyDto;
import cn.shopex.hobbits.ms.harbor.common.log.RecordContext;
import cn.shopex.hobbits.ms.harbor.common.log.RecordContextHolder;
import cn.shopex.hobbits.ms.harbor.common.order.constant.InvoiceApplyFieldConst;
import cn.shopex.hobbits.ms.harbor.common.order.conversion.OriginalInvoiceConversion;
import cn.shopex.hobbits.ms.harbor.common.service.OtherCenterDataService;
import cn.shopex.hobbits.ms.harbor.configuration.ApplicationMqProperties;
import cn.shopex.hobbits.ms.harbor.util.SpringContextUtil;
import cn.shopex.hobbits.ms.harbor.web.order.invoice.enums.AlibabaInvoiceStatusEnum;
import cn.shopex.hobbits.ms.harbor.web.order.invoice.enums.DouYinInvoiceStatusEnum;
import cn.shopex.hobbits.ms.harbor.web.order.invoice.mapper.OriginalInvoiceBizMapper;
import cn.shopex.hobbits.ms.harbor.web.order.invoice.service.OriginalInvoiceCommonService;
import cn.shopex.hobbits.ms.library.exception.BizServiceException;
import cn.shopex.hobbits.ms.library.redis.RedisLockService;
import cn.shopex.hobbits.ms.orm.mapper.OriginalInvoiceHistoryMapper;
import cn.shopex.hobbits.ms.orm.mapper.OriginalInvoiceMapper;
import cn.shopex.hobbits.ms.orm.model.OriginalInvoice;
import cn.shopex.hobbits.ms.orm.model.OriginalInvoiceHistory;
import cn.shopex.hobbits.ms.orm.service.OriginalInvoiceHistoryService;
import cn.shopex.hobbits.ms.rmp.biz.producer.RMQCommProducer;
import cn.shopex.hobbits.ms.rmp.biz.producer.domain.RMQProducerBody;
import cn.shopex.hobbits.neb.domain.InvoiceApplyItem;
import cn.shopex.hobbits.neb.domain.InvoiceApplyItemDto;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

import static cn.shopex.hobbits.ms.library.utils.DateUtil.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OriginalInvoiceCommonServiceImpl implements OriginalInvoiceCommonService {
    private final OriginalInvoiceBizMapper originalInvoiceBizMapper;
    private final OriginalInvoiceMapper originalInvoiceMapper;
    private final RedisLockService redisLockService;
    private final ApplicationMqProperties applicationMqProperties;
    private final RMQCommProducer rmqCommProducer;
    private final ObjectMapper objectMapper;
    private final OtherCenterDataService otherCenterDataService;
    private final OriginalInvoiceHistoryMapper originalInvoiceHistoryMapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
//    @OperateRecord(title = "原始开票申请", menuId = "", isHttpRequest = false, metadataQuerySwitch = false)
    public void saveOriginalInvoice(OriginalInvoiceHistory originalInvoiceHistory) {
        //来源店铺
        OnlineStoreDto onlineStoreDto = otherCenterDataService.getOnlineStore(originalInvoiceHistory.getSystemNode()).get(0);
        String invoiceDetails = originalInvoiceHistory.getInvoiceDetails();
        OriginalInvoiceTransformer originalInvoiceTransformer;
        if (InvoiceApplyFieldConst.ONLINE_STORE_TYPE_2.equals(onlineStoreDto.getType())) {
            //抖音
            originalInvoiceTransformer = SpringContextUtil.getBean(DouYinOriginalInvoiceTransformer.class);
        } else if(CommonConst.SOURCE_PLATFORM_PDD.equals(onlineStoreDto.getType())){
            originalInvoiceTransformer = SpringContextUtil.getBean(PddOriginalInvoiceTransformer.class);
        } else {
            //淘系
            originalInvoiceTransformer = SpringContextUtil.getBean(AlibabaOriginalInvoiceTransformer.class);
        }
        InvoiceApplyDto invoiceApplyDto = originalInvoiceTransformer.transform(invoiceDetails);
        if (Objects.nonNull(invoiceApplyDto)) {
            invoiceApplyDto.setPlatformType(onlineStoreDto.getType());
            invoiceApplyDto.setStoreCode(onlineStoreDto.getCode());
            handleInvoiceApplyDto(invoiceApplyDto, onlineStoreDto);
        }
    }

    private void handleInvoiceApplyDto(InvoiceApplyDto invoiceApplyDto, OnlineStoreDto onlineStoreDto) {
        String transactionNumber = invoiceApplyDto.getPlatformTid();
        log.debug("原始开票申请开始转换 平台订单号: {}", transactionNumber);
        LocalDateTime now = LocalDateTime.now();
        String operateTime = formatDate(now, FMT_sdf17);
        Long unixTime = formatUnixTime(now);

        // 加redis锁
        boolean isLock = false;
        try {
            isLock = this.redisLockService.lock(transactionNumber);
            if (!isLock) {
                throw new BizServiceException(MessageConst.LOCK_DATA_FAILURE_CODE, MessageConst.LOCK_DATA_FAILURE_CONTENT);
            }
            OriginalInvoiceHistory originalInvoiceHistory = this.originalInvoiceBizMapper.selectLatestOriginalInvoiceHistory(transactionNumber);
            if (originalInvoiceHistory == null) {
                log.warn("保存开票申请数据信息-抖音原始开票申请履历为空");
                return;
            }
            OriginalInvoice originalInvoice = this.originalInvoiceBizMapper.selectOriginalInvoiceByTransactionNumber(transactionNumber).orElse(null);
            if (originalInvoice != null) {
                // 平台最后更新时间
                Long lastModifiedTimeUxt = invoiceApplyDto.getGmtModifiedStr();
                log.info("原始开票申请信息 平台最后更新时间 lastModifiedTimeUxt: {}  新申请开票时间:{}", lastModifiedTimeUxt, originalInvoice.getLastModifiedTimeUxt());
                // 如果 invoiceInfo的平台最后更新时间 < 原单最后更新时间 return
                if (lastModifiedTimeUxt <= originalInvoice.getLastModifiedTimeUxt()) {
                    log.warn("原始开票申请 -- 平台最后更新时间小于等于原单最后更新时间");
                    return;
                }
                // 如果原始开票申请数据状态为转换中或者已转换，则不可以被转换
                if (Objects.equals(originalInvoice.getConversionStatus(), CommonConst.CONVERT_STATUS_MIDDLE)
                        || Objects.equals(originalInvoice.getConversionStatus(), CommonConst.CONVERT_STATUS_ALREADY)) {

                    //pdd 平台的开票申请状态为转换中 且触发状态为invoice_change 释放锁
                    if(Objects.equals(originalInvoice.getConversionStatus(), CommonConst.CONVERT_STATUS_ALREADY) && CommonConst.SOURCE_PLATFORM_PDD.equals(onlineStoreDto.getType()) && Objects.equals(invoiceApplyDto.getTriggerStatus(), "invoice_change")){
                        log.info("拼多多发票修改抬头， transactionNumber: {}", transactionNumber);
                    }else{
                        log.info("原始开票申请状态为转换中  已释放锁 transactionNumber: {}", transactionNumber);
                        return;
                    }
                }
            }
            // 设置原始开票申请信息
            originalInvoice = fillOriginalInvoice(originalInvoice, invoiceApplyDto, objectMapper.writeValueAsString(invoiceApplyDto), operateTime, unixTime);
            // 来源平台
            originalInvoice.setSourcePlatform(onlineStoreDto.getType());
            // 来源店铺
            originalInvoice.setStoreCode(onlineStoreDto.getCode());
            Integer status = invoiceApplyDto.getStatus();
            boolean push = true;
            if (InvoiceApplyFieldConst.ONLINE_STORE_TYPE_2.equals(onlineStoreDto.getType())) {
                //抖音 已开票和已关闭的不需要推送
                if (DouYinInvoiceStatusEnum.INVOICED.getCode().equals(status)
                        || DouYinInvoiceStatusEnum.CLOSED.getCode().equals(status)) {
                    originalInvoice.setConversionStatus(CommonConst.CONVERT_STATUS_ALREADY);
                    originalInvoice.setConversionDescription(CommonConst.BW_INVOICE_CONVERSION_ALREADY);
                    push = false;
                }
                originalInvoice.setInvoicingStatus(DouYinInvoiceStatusEnum.getDescByCode(status));
            } else {
                originalInvoice.setInvoicingStatus(status == null ? AlibabaInvoiceStatusEnum.APPLYING.getDesc() : AlibabaInvoiceStatusEnum.getDescByCode(status));
            }
            String operateType;
            // 保存原始订单信息
            if (originalInvoice.getId() == null) {
                operateType = "新增";
                this.originalInvoiceMapper.create(originalInvoice);
            } else {
                operateType = "修改";
                this.originalInvoiceMapper.update(originalInvoice);
            }
            log.info("原始开票申请数据推送到财务中心 invoiceApplyDto：{}", JSONObject.toJSONString(invoiceApplyDto));
            // 发送MQ给财务中心
            if (push) {
                /**
                if (CommonConst.SOURCE_PLATFORM_TB.equals(onlineStoreDto.getType())) {
                    InvoiceApplyItemDto invoiceItems = invoiceApplyDto.getInvoiceItems();
                    List<InvoiceApplyItem> invoiceItem = invoiceItems.getInvoiceItem();
                    List<InvoiceApplyItem> newInvoiceItem = Lists.newArrayList();
                    invoiceItems.setInvoiceItem(newInvoiceItem);
                    for (InvoiceApplyItem item : invoiceItem) {
                        //排除掉折扣行
                        if (CommonConst.BW_INVOICE_ROW_TYPE_DISCOUNT.equals(item.getRowType())) {
                            continue;
                        }
                        InvoiceApplyItem newInvoiceItems = new InvoiceApplyItem();
                        BeanUtils.copyProperties(item, newInvoiceItems);
                        newInvoiceItem.add(newInvoiceItems);
                    }
                }
                this.sendMq(applicationMqProperties.getOrderInvoiceApply(), MessageQueueConst.MQ_BIZ_TYPE_ORDER_INVOICE_APPLY, invoiceApplyDto);
                 */
                sendOriginalInvoice2FinanceMq(invoiceApplyDto);
            }
            try {
                RecordContext<Object, String> recordContext = new RecordContext<>(originalInvoice.getTransactionNumber(), CommonConst.DEFAULT_OPERATOR, operateTime, unixTime);
                recordContext.setOperateType(operateType);
                RecordContextHolder.createRecordContext(recordContext);
            } catch (Exception e) {
                log.error("原始开票申请添加操作日志失败: originalInvoice : {}", originalInvoice, e);
            }
        } catch (Exception ex) {
            log.error("保存原始开票申请-数据转换异常  [invoiceApplyDto:{}]", invoiceApplyDto, ex);
            BizServiceException bse;
            if (ex instanceof BizServiceException) {
                bse = (BizServiceException) ex;
            } else {
                bse = new BizServiceException(MessageConst.SYSTEM_ERROR_CODE, ex.getMessage());
            }
            throw bse;
        } finally {
            if (isLock) {
                try {
                    this.redisLockService.unlock(transactionNumber);
                } catch (Exception e) {
                    log.error("原始开票申请下载 redis解锁失败", e);
                }
            }
        }
    }

    public static void main(String[] args) {
        String ss="{\"applyId\":\"01mMbzwrnR3WpTyd5M1hXITvQAVYsh2K62wVpiVvtGm5E\",\"businessType\":1,\"extendProps\":\"{}\",\"gmtCreate\":1753817509000,\"gmtModifiedStr\":1753817509000,\"invoiceAmount\":\"28.97\",\"invoiceItems\":{\"invoiceItem\":[{\"amount\":\"37.00\",\"bizOrderId\":\"2854380972494492761\",\"itemName\":\"宝路狗零食洁齿棒宠物洁磨牙棒75g\",\"price\":\"33.240000\",\"quantity\":\"2\",\"rowType\":\"2\",\"specification\":\"10154670\"},{\"amount\":\"-8.03\",\"bizOrderId\":\"2854380972494492761\",\"itemName\":\"宝路狗零食洁齿棒宠物洁磨牙棒75g\",\"rowType\":\"1\"}]},\"invoiceKind\":0,\"invoiceType\":1,\"payerMail\":\"\",\"payerName\":\"黄春喜\",\"platformCode\":\"TM\",\"platformTid\":\"2854380972494492761\",\"platformType\":1,\"status\":1,\"storeCode\":\"TM_BDB_FGS\",\"triggerStatus\":\"buyer_payed\"}";
        InvoiceApplyDto invoiceApplyDto = JSON.parseObject(ss, InvoiceApplyDto.class);
        InvoiceApplyItemDto invoiceItems = invoiceApplyDto.getInvoiceItems();
        List<InvoiceApplyItem> invoiceItem = invoiceItems.getInvoiceItem();
        List<InvoiceApplyItem> newInvoiceItem = Lists.newArrayList();
        invoiceItems.setInvoiceItem(newInvoiceItem);
        for (InvoiceApplyItem item : invoiceItem) {
            //排除掉折扣行
            if (CommonConst.BW_INVOICE_ROW_TYPE_DISCOUNT.equals(item.getRowType())) {
                continue;
            }
            InvoiceApplyItem newInvoiceItems = new InvoiceApplyItem();
            BeanUtils.copyProperties(item, newInvoiceItems);
            newInvoiceItem.add(newInvoiceItems);
        }
        log.info("{}",JSON.toJSONString(invoiceApplyDto));
    }


    @Override
//    @OperateRecord(operateType = "转换", menuId = "", isHttpRequest = false, metadataQuerySwitch = false)
    public void updateOriginalInvoiceConversionResult(ConversionOriginalInvoiceResultDto conversionOriginalInvoiceResultDto) {
        LocalDateTime now = LocalDateTime.now();
        String operateTime = formatDate(now, FMT_sdf17);
        Long unixTime = formatUnixTime(now);
        log.debug("原始开票申请转换结果更新 平台订单号: {}", conversionOriginalInvoiceResultDto.getPlatformTid());
        // 获取原始订单
        OriginalInvoice originalInvoice = this.originalInvoiceBizMapper.selectOriginalInvoiceByTransactionNumber(conversionOriginalInvoiceResultDto.getPlatformTid()).orElse(null);
        if (originalInvoice == null) {
            if (Objects.equals(CommonConst.STORE_TYPE_JD, conversionOriginalInvoiceResultDto.getPlatformType())) {
                //发票申请信息详情
                InvoiceApplyDto invoiceApplyDto = conversionOriginalInvoiceResultDto.getInvoiceApplyDto();
                if (!Objects.isNull(invoiceApplyDto)) {
                    // 设置原始开票申请信息
                    originalInvoice = this.fillOriginalInvoice(null, invoiceApplyDto, StringUtils.EMPTY, operateTime, unixTime);
                    // 来源平台
                    originalInvoice.setSourcePlatform(invoiceApplyDto.getPlatformType());
                    // 来源店铺
                    originalInvoice.setStoreCode(invoiceApplyDto.getPlatformCode());
                    //原始开票申请详细信息
                    try {
                        originalInvoice.setInvoiceDetails(new ObjectMapper().writeValueAsString(invoiceApplyDto));
                    } catch (JsonProcessingException e) {
                        originalInvoice.setInvoiceDetails(StringUtils.EMPTY);
                        log.warn("京东发票申请信息详情转JSON字段串异常", e);
                    }
                    this.originalInvoiceMapper.create(originalInvoice);
                }
            } else {
                log.warn("修改原始开票申请转换结果 未查询到原始开票申请信息 等待重试 conversionOriginalInvoiceResultDto: {}", conversionOriginalInvoiceResultDto);
                throw new BizServiceException(MessageConst.DATA_NOT_FOUND_CODE, MessageConst.DATA_NOT_FOUND_CONTENT);
            }
        }
        String transactionNumber = conversionOriginalInvoiceResultDto.getPlatformTid();
        // 加redis锁
        boolean isLock = this.redisLockService.lock(transactionNumber);
        try {
            if (!isLock) {
                throw new BizServiceException(MessageConst.LOCK_DATA_FAILURE_CODE, MessageConst.LOCK_DATA_FAILURE_CONTENT);
            }
            // 是否需要再次转换
            boolean isAgentConversion = false;
            Integer isSuccess = null;
            Integer conversionStatus = null;
            int retryTimes;
            String conversionDescription = StringUtils.EMPTY;
            int conversionTimes;
            if (conversionOriginalInvoiceResultDto.isSuccess()) {
                // 转换成功
                if (Objects.equals(originalInvoice.getIsSuccess(), CommonConst.COMMON_FALSE)) {
                    isSuccess = CommonConst.COMMON_TRUE;
                }
                conversionStatus = CommonConst.CONVERT_STATUS_ALREADY;
                // 重置重试次数
                retryTimes = 0;
            } else {
                // 转换失败
                // 改为接口中心定时重试
                int newRetryTimes = originalInvoice.getRetryTimes() + 1;
                conversionStatus = CommonConst.CONVERT_STATUS_FAIL;
                // 如果失败次数大于3次，不再继续重试
//                if (newRetryTimes >= CommonConst.FAIL_RETRY_TIMES) {
//                    conversionStatus = CommonConst.CONVERT_STATUS_FAIL;
//                } else {
//                    isAgentConversion = true;
//                }
                // 重试次数
                retryTimes = newRetryTimes;
            }
            if (StringUtils.isNotBlank(conversionOriginalInvoiceResultDto.getConversionDescription())) {
                conversionDescription = conversionOriginalInvoiceResultDto.getConversionDescription();
            }
            conversionTimes = originalInvoice.getConversionTimes() + 1;
            if(conversionTimes >CommonConst.FAIL_RETRY_TIMES){
                conversionTimes = CommonConst.FAIL_RETRY_TIMES;
            }
            if(retryTimes >CommonConst.FAIL_RETRY_TIMES){
                retryTimes = CommonConst.FAIL_RETRY_TIMES;
            }
            this.originalInvoiceBizMapper.updateOriginalInvoiceConversionResult(
                    transactionNumber,
                    isSuccess,
                    conversionStatus,
                    retryTimes,
                    conversionDescription,
                    conversionTimes,
                    CommonConst.DEFAULT_OPERATOR, operateTime, unixTime);
            // 只保存成功 或最后一次重试的日志
            if (conversionStatus != null) {
                Map<String, List<String>> map = new HashMap<>(1);
                try {
                    RecordContext<Object, Object> context = new RecordContext<>();
                    if (Objects.equals(conversionStatus, CommonConst.CONVERT_STATUS_ALREADY)) {
                        map.put(transactionNumber, Collections.singletonList("转换成功"));
                    } else {
                        map.put(transactionNumber, Collections.singletonList("转换失败, 原因是: ".concat(conversionDescription)));
                    }
                    context.setMeteDataOperateContentGroup(map);
                    context.setOperate(CommonConst.DEFAULT_OPERATOR);
                    context.setOperateTime(operateTime);
                    context.setOperateTimeUxt(unixTime);
                    RecordContextHolder.createRecordContext(context);
                } catch (Exception e) {
                    log.warn("转换原始开票申请数据添加操作日志失败 map: {}", map, e);
                }
            }

            if (isAgentConversion) {
                //开票申请通用实体
                InvoiceApplyDto dto = null;
                if (Objects.equals(CommonConst.STORE_TYPE_JD, conversionOriginalInvoiceResultDto.getPlatformType())) {
                    //发票申请信息详情
                    InvoiceApplyDto invoiceApplyDto = conversionOriginalInvoiceResultDto.getInvoiceApplyDto();
                    if (!Objects.isNull(invoiceApplyDto)) {
                        dto = invoiceApplyDto;
                    }
                } else {
                    dto = new InvoiceApplyDto();
                    Map<String, Object> invoiceInfo = this.objectMapper.readValue(originalInvoice.getInvoiceDetails(), new TypeReference<Map<String, Object>>() {
                    });
                    //来源店铺
                    OnlineStoreDto onlineStoreDto = otherCenterDataService.getOnlineStore(invoiceInfo.get(InvoiceApplyFieldConst.SYSTEM_NODE).toString()).get(0);
                    dto.setPlatformType(onlineStoreDto.getType());
                    dto.setStoreCode(onlineStoreDto.getCode());
                    // 转换原始开票申请信息为通用实体
                    OriginalInvoiceConversion.createInvoiceApplyDto(dto, invoiceInfo);
                }
                // 发送MQ给财务中心
                //this.sendMq(applicationMqProperties.getOrderInvoiceApply(), MessageQueueConst.MQ_BIZ_TYPE_ORDER_INVOICE_APPLY, dto);
                sendOriginalInvoice2FinanceMq(dto);
            }
        } catch (Exception ex) {
            if (ex instanceof JsonProcessingException) {
                log.warn("修改原始开票申请转换结果 原始开票申请转换共通实体异常", ex);
                return;
            }
            BizServiceException bse;
            if (ex instanceof BizServiceException) {
                bse = (BizServiceException) ex;
            } else {
                bse = new BizServiceException(MessageConst.SYSTEM_ERROR_CODE, ex.getMessage());
            }
            log.warn("修改原始开票申请转换结果 转换异常", ex);
        } finally {
            try {
                this.redisLockService.unlock(transactionNumber);
            } catch (Exception e) {
                log.warn("原始开票申请转换结果解锁失败");
            }
        }
    }

    @Override
    public OriginalInvoice fillOriginalInvoice(OriginalInvoice originalInvoice, InvoiceApplyDto invoiceApplyDto, String invoiceDetails, String operateTime, Long unixTime) {
        if (originalInvoice == null) {
            // 新建原单
            originalInvoice = new OriginalInvoice();
            originalInvoice.setTransactionNumber(invoiceApplyDto.getPlatformTid());
            originalInvoice.setCreateTime(operateTime);
            originalInvoice.setCreateTimeUxt(unixTime);
            originalInvoice.setCreateUser(CommonConst.DEFAULT_OPERATOR);
            originalInvoice.setConversionTimes(0);
            originalInvoice.setRetryTimes(0);
            // 设置是否已转换成功为：否
            originalInvoice.setIsSuccess(CommonConst.COMMON_FALSE);
        }
        // 设置转换状态为转换中
        originalInvoice.setConversionStatus(CommonConst.CONVERT_STATUS_MIDDLE);
        originalInvoice.setConversionDescription(StringUtils.EMPTY);
        originalInvoice.setUpdateTime(operateTime);
        originalInvoice.setUpdateTimeUxt(unixTime);
        originalInvoice.setUpdateUser(CommonConst.DEFAULT_OPERATOR);
        // 填充原始开票申请基础信息
       /* Integer status = invoiceApplyDto.getStatus();
        if (status != null) {
            originalInvoice.setInvoicingStatus(String.valueOf(invoiceApplyDto.getStatus()));
        }*/
        originalInvoice.setInvoiceTitle(invoiceApplyDto.getPayerName());
        originalInvoice.setInvoiceTitleType(invoiceApplyDto.getBusinessType());
        originalInvoice.setSourceInvoicingType(invoiceApplyDto.getInvoiceKind());
        originalInvoice.setInvoiceType(invoiceApplyDto.getInvoiceType());
        BigDecimal invoiceAmount = new BigDecimal(0);
        try {
            invoiceAmount = new BigDecimal(invoiceApplyDto.getInvoiceAmount());
        } catch (Exception e) {
            log.warn("开票金额转换异常, invoiceAmount: {}, msg: {}", invoiceApplyDto.getInvoiceAmount(), e.getMessage());
        }
        originalInvoice.setInvoiceAmount(invoiceAmount);
        originalInvoice.setLastModifiedTimeUxt(invoiceApplyDto.getGmtModifiedStr());
        // 原始开票申请数据转json
        originalInvoice.setInvoiceDetails(invoiceDetails);
        return originalInvoice;
    }

    @Override
    public void sendOriginalInvoice2FinanceMq(InvoiceApplyDto invoiceApplyDto) {
        ApplicationMqProperties.Topic topic=applicationMqProperties.getOrderInvoiceApply();
        String bizType=MessageQueueConst.MQ_BIZ_TYPE_ORDER_INVOICE_APPLY;
        log.info("原始开票申请发送转换MQ消息 start topic: {}, bizType: {}, invoiceApplyDto: {}", topic, bizType, invoiceApplyDto);
        //不同平台，推送判断或过滤
        if (CommonConst.SOURCE_PLATFORM_DY.equals(invoiceApplyDto.getPlatformType())) {
            if (DouYinInvoiceStatusEnum.INVOICED.getCode().equals(invoiceApplyDto.getStatus())
                    || DouYinInvoiceStatusEnum.CLOSED.getCode().equals(invoiceApplyDto.getStatus())) {
                return;
            }
        }else if (CommonConst.SOURCE_PLATFORM_TB.equals(invoiceApplyDto.getPlatformType())) {
            InvoiceApplyItemDto invoiceItems = invoiceApplyDto.getInvoiceItems();
            List<InvoiceApplyItem> invoiceItem = invoiceItems.getInvoiceItem();
            List<InvoiceApplyItem> newInvoiceItem = Lists.newArrayList();
            for (InvoiceApplyItem item : invoiceItem) {
                //排除掉折扣行
                if (CommonConst.BW_INVOICE_ROW_TYPE_DISCOUNT.equals(item.getRowType())) {
                    continue;
                }
                InvoiceApplyItem newInvoiceItems = new InvoiceApplyItem();
                BeanUtils.copyProperties(item, newInvoiceItems);
                newInvoiceItem.add(newInvoiceItems);
            }
            invoiceItems.setInvoiceItem(newInvoiceItem);
        }
        try {
            this.rmqCommProducer.sendSyncMsg(topic.getTopic(),
                    topic.getTag(), new RMQProducerBody(bizType, invoiceApplyDto));
        } catch (Exception e) {
            log.warn("原始开票申请发送转换MQ消息异常 topic: {}, bizType: {}", topic, bizType, e);
        }
        log.info("原始开票申请发送转换MQ消息 end topic: {}, bizType: {}", topic, bizType);
    }


    /**
     * 发送MQ消息
     *
     * @param topic     topic
     * @param bizType   业务类型
     * @param orderInfo 原始订单信息
     */
//    private void sendMq(ApplicationMqProperties.Topic topic, String bizType, Object orderInfo) {
//        log.info("原始开票申请发送转换MQ消息 start topic: {}, bizType: {}, orderInfo: {}", topic, bizType, orderInfo);
//        try {
//            this.rmqCommProducer.sendSyncMsg(topic.getTopic(),
//                    topic.getTag(), new RMQProducerBody(bizType, orderInfo));
//        } catch (Exception e) {
//            log.warn("原始开票申请发送转换MQ消息异常 topic: {}, bizType: {}", topic, bizType, e);
//        }
//        log.info("原始开票申请发送转换MQ消息 end topic: {}, bizType: {}", topic, bizType);
//    }
}
