package cn.shopex.hobbits.ms.harbor.api.inner.biz;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.shopex.hobbits.ms.harbor.api.inner.common.ApiCommonConst;
import cn.shopex.hobbits.ms.harbor.api.inner.common.OrderSourceEnum;
import cn.shopex.hobbits.ms.harbor.api.inner.domain.*;
import cn.shopex.hobbits.ms.harbor.common.MessageConst;
import cn.shopex.hobbits.ms.harbor.common.WmsParamConst;
import cn.shopex.hobbits.ms.library.domain.OrderDecryptDto;
import cn.shopex.hobbits.ms.library.encrypt.ReversibleEncrypt;
import cn.shopex.hobbits.ms.library.redundant.base.domain.OnlineStoreDto;
import cn.shopex.hobbits.ms.library.redundant.base.domain.WarehouseThirdPartyDto;
import cn.shopex.hobbits.ms.library.redundant.base.service.BaseRedundantService;
import cn.shopex.hobbits.ms.library.redundant.configuration.domain.RedundantShopexAuthorization;
import cn.shopex.hobbits.ms.harbor.common.utils.BeanCopyUtils;
import cn.shopex.hobbits.ms.harbor.web.order.decryption.service.OrderDecryptionService;
import cn.shopex.hobbits.ms.library.exception.BizDataNotFoundException;
import cn.shopex.hobbits.ms.library.exception.BizServiceException;
import cn.shopex.hobbits.ms.library.redundant.configuration.service.ApplicationRedundantService;
import cn.shopex.hobbits.ms.orm.model.*;
import cn.shopex.hobbits.neb.MatrixClient;
import cn.shopex.hobbits.neb.domain.RelatedOrder;
import cn.shopex.hobbits.neb.domain.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringEscapeUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static cn.shopex.hobbits.ms.library.utils.DateUtil.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WmsSyncOrderBiz {

    private final MatrixClient matrixClient;
    private final ApplicationRedundantService applicationRedundantService;
    private final OrderDecryptionService orderDecryptionService;
    private final BaseRedundantService baseRedundantService;

    /**
     * wms发货结果单取消
     *
     * @param retailInvoiceWmsReq 处理批号
     */
    public MatrixResponse wmsSyncCancelOrder(RetailInvoiceWmsReq retailInvoiceWmsReq) {
        RetailInvoiceWmsParameter orderCancelParameter = RetailInvoiceWmsParameter.builder().build();
        BeanUtils.copyProperties(retailInvoiceWmsReq, orderCancelParameter);
        return matrixClient.doActionWmsOrderCancel(orderCancelParameter);
    }


    /**
     * WMS发货单创建
     *
     * @param matrixWmsDeliveryCreateReq 发货单创建参数
     * @return WMS发货单号
     */
    public MatrixResponse wmsDeliveryCreate(MatrixWmsDeliveryCreateReq matrixWmsDeliveryCreateReq) {
        try {
            // 获取授权信息
            RedundantShopexAuthorization authorization = this.applicationRedundantService.selectShopexAuthorizationToken();
            if (authorization == null) {
                throw new BizDataNotFoundException(MessageConst.NOT_AUTHORIZATION_INFO_CODE, MessageConst.NOT_AUTHORIZATION_INFO_CONTENT);
            }
            WmsDeliveryCreateReq wmsDeliveryCreateReq = new WmsDeliveryCreateReq();
            BeanUtils.copyProperties(matrixWmsDeliveryCreateReq, wmsDeliveryCreateReq);
            List<WmsDeliveryCreateDetailReq> items = BeanCopyUtils.copyListProperties(matrixWmsDeliveryCreateReq.getItems(), WmsDeliveryCreateDetailReq::new);
            wmsDeliveryCreateReq.setItems(items);
            wmsDeliveryCreateReq.setFromNodeId(authorization.getNode());
            wmsDeliveryCreateReq.setCertificate(authorization.getCertificate());
            wmsDeliveryCreateReq.setToken(authorization.getToken());
            // 不同平台参数不同
            OrderSourceEnum orderSourceEnum = OrderSourceEnum.getOrderSourceEnumByStoreType(wmsDeliveryCreateReq.getOnlineStoreType());
            log.debug("通知WMS下发 发货单编号: {} 订单来源枚举: {}", wmsDeliveryCreateReq.getOutOrderCode(), orderSourceEnum);
            wmsDeliveryCreateReq.setOrderSource(orderSourceEnum.getOrderSource());
            wmsDeliveryCreateReq.setOrderSourceName(orderSourceEnum.getOrderSourceName());
            switch (orderSourceEnum) {
                case TB:
                    OrderDecryptionTaobao orderDecryptionTaobao =
                            this.orderDecryptionService.selectOrderDecryptionTaobaoByReceiptNumberAndReceiptType(wmsDeliveryCreateReq.getTid(), wmsDeliveryCreateReq.getDecryptionOrderType());
                    if (Objects.isNull(orderDecryptionTaobao)) {
                        break;
                    }
                    wmsDeliveryCreateReq.setReceiverOaid(orderDecryptionTaobao.getOaid());
                    break;
                case JD:
                    OrderDecryptionJD orderDecryptionJD =
                            this.orderDecryptionService.selectOrderDecryptionJDByReceiptNumberAndReceiptType(wmsDeliveryCreateReq.getTid(), wmsDeliveryCreateReq.getDecryptionOrderType());
                    if (Objects.isNull(orderDecryptionJD)) {
                        break;
                    }
                    wmsDeliveryCreateReq.setReceiverOaid(orderDecryptionJD.getOaid());
                    break;
                case DY:
                    OrderDecryptionDouyin orderDecryptionDouyin =
                            this.orderDecryptionService.selectOrderDecryptionDouyinByReceiptNumberAndReceiptType(wmsDeliveryCreateReq.getTid(), wmsDeliveryCreateReq.getDecryptionOrderType());
                    if (Objects.isNull(orderDecryptionDouyin)) {
                        break;
                    }
                    // 设置收件人信息为密文信息
                    wmsDeliveryCreateReq.setReceiverName(orderDecryptionDouyin.getReceiverName());
                    wmsDeliveryCreateReq.setReceiverMobile(orderDecryptionDouyin.getReceiverMobile());
                    wmsDeliveryCreateReq.setReceiverAddress(orderDecryptionDouyin.getReceiverAddress());
                    wmsDeliveryCreateReq.setReceiverPhone(orderDecryptionDouyin.getReceiverMobile());
                    break;
                case PDD:
                    OrderDecryptionPinduoduo orderDecryptionPinduoduo =
                            this.orderDecryptionService.selectOrderDecryptionPinduoduoByReceiptNumberAndReceiptType(wmsDeliveryCreateReq.getTid(), wmsDeliveryCreateReq.getDecryptionOrderType());
                    if (Objects.isNull(orderDecryptionPinduoduo)) {
                        break;
                    }
                    // 设置收件人信息为密文信息
                    wmsDeliveryCreateReq.setReceiverName(orderDecryptionPinduoduo.getReceiverName());
                    wmsDeliveryCreateReq.setReceiverMobile(orderDecryptionPinduoduo.getReceiverMobile());
                    wmsDeliveryCreateReq.setReceiverAddress(orderDecryptionPinduoduo.getReceiverAddress());
                    wmsDeliveryCreateReq.setReceiverPhone(orderDecryptionPinduoduo.getReceiverMobile());
                    break;
                case XHS:
                    OrderDecryptionXiaohongshu orderDecryptionXiaohongshu =
                            this.orderDecryptionService.selectOrderDecryptionXiaohongshuByReceiptNumberAndReceiptType(
                                    wmsDeliveryCreateReq.getTid(), wmsDeliveryCreateReq.getDecryptionOrderType());
                    if (Objects.isNull(orderDecryptionXiaohongshu)) {
                        break;
                    }
                    // 设置收件人信息为密文信息
                    wmsDeliveryCreateReq.setReceiverName(orderDecryptionXiaohongshu.getReceiverName());
                    wmsDeliveryCreateReq.setReceiverMobile(orderDecryptionXiaohongshu.getReceiverMobile());
                    wmsDeliveryCreateReq.setReceiverAddress(orderDecryptionXiaohongshu.getReceiverAddress());
                    wmsDeliveryCreateReq.setReceiverPhone(orderDecryptionXiaohongshu.getReceiverMobile());
                    wmsDeliveryCreateReq.setReceiverOaid(matrixWmsDeliveryCreateReq.getOaid());
                    break;
                case WPH:
                    OrderDecryptionWeipinhui orderDecryptionWeipinhui =
                            this.orderDecryptionService.selectOrderDecryptionWeipinhuiByReceiptNumberAndReceiptType(
                                    wmsDeliveryCreateReq.getTid(), wmsDeliveryCreateReq.getDecryptionOrderType());
                    if (Objects.isNull(orderDecryptionWeipinhui)) {
                        break;
                    }
                    OrderDecryptDto orderDecryptDtoWph = ReversibleEncrypt.parseOaid(orderDecryptionWeipinhui.getOaid());
                    // 设置收件人信息为解密后的明文信息
                    wmsDeliveryCreateReq.setReceiverName(orderDecryptDtoWph.getReciNm());
                    wmsDeliveryCreateReq.setReceiverMobile(orderDecryptDtoWph.getReciPhone());
                    wmsDeliveryCreateReq.setReceiverAddress(orderDecryptDtoWph.getReciAddr());
                    wmsDeliveryCreateReq.setReceiverPhone(orderDecryptDtoWph.getReciPhone());
                    break;
                case KS:
                    OrderDecryptionKs orderDecryptionKs =
                            this.orderDecryptionService.selectOrderDecryptionKsByReceiptNumberAndReceiptType(
                                    wmsDeliveryCreateReq.getTid(), wmsDeliveryCreateReq.getDecryptionOrderType());
                    if (Objects.isNull(orderDecryptionKs)) {
                        break;
                    }
                    // 设置收件人信息为密文信息
                    wmsDeliveryCreateReq.setReceiverName(orderDecryptionKs.getReceiverName());
                    wmsDeliveryCreateReq.setReceiverMobile(orderDecryptionKs.getReceiverMobile());
                    wmsDeliveryCreateReq.setReceiverAddress(orderDecryptionKs.getReceiverAddress());
                    wmsDeliveryCreateReq.setReceiverPhone(orderDecryptionKs.getReceiverMobile());
                    break;
                case DEWU:
                    OrderDecryptionDewu orderDecryptionDewu =
                            this.orderDecryptionService.selectOrderDecryptionDewuByReceiptNumberAndReceiptType(
                                    wmsDeliveryCreateReq.getTid(), wmsDeliveryCreateReq.getDecryptionOrderType());
                    if (Objects.isNull(orderDecryptionDewu)) {
                        break;
                    }
                    // 设置收件人信息为密文信息
                    OrderDecryptDto orderDecryptDtoDewu = ReversibleEncrypt.parseOaid(orderDecryptionDewu.getOaid());
                    wmsDeliveryCreateReq.setReceiverName(orderDecryptDtoDewu.getReciNm());
                    wmsDeliveryCreateReq.setReceiverMobile(orderDecryptDtoDewu.getReciPhone());
                    wmsDeliveryCreateReq.setReceiverAddress(orderDecryptDtoDewu.getReciAddr());
                    wmsDeliveryCreateReq.setReceiverPhone(orderDecryptDtoDewu.getReciTel());
                    break;
                case SI_PRIVATE:
                    OrderDecryptionPrivate orderDecryptionPrivate = this.orderDecryptionService.selectOrderDecryptionPrivateByReceiptNumberAndReceiptType(wmsDeliveryCreateReq.getTid(), wmsDeliveryCreateReq.getDecryptionOrderType());
                    if (Objects.isNull(orderDecryptionPrivate)) {
                        break;
                    }
                    // 设置收件人信息为明文信息
                    OrderDecryptDto orderDecryptDto = ReversibleEncrypt.parseOaid(orderDecryptionPrivate.getOaid());
                    wmsDeliveryCreateReq.setReceiverName(orderDecryptDto.getReciNm());
                    wmsDeliveryCreateReq.setReceiverMobile(orderDecryptDto.getReciPhone());
                    wmsDeliveryCreateReq.setReceiverAddress(orderDecryptDto.getReciAddr());
                    wmsDeliveryCreateReq.setReceiverPhone(orderDecryptDto.getReciTel());
                    break;
                case WXSPH:
                    OrderDecryptionWechatStore orderDecryptionWechatStore =
                            this.orderDecryptionService.selectOrderDecryptionWechatStoreByReceiptNumberAndReceiptType(wmsDeliveryCreateReq.getTid(), wmsDeliveryCreateReq.getDecryptionOrderType());
                    if (Objects.isNull(orderDecryptionWechatStore)) {
                        break;
                    }
                    wmsDeliveryCreateReq.setReceiverOaid(orderDecryptionWechatStore.getOaid());
                    wmsDeliveryCreateReq.setPlatformOrderCode(orderDecryptionWechatStore.getReceiptNumber());
                    break;
                default:
                    break;
            }
            log.info("通知WMS下发 发货单编号: {} 参数: {}", matrixWmsDeliveryCreateReq.getOutOrderCode(), wmsDeliveryCreateReq);
            MatrixResponse matrixResponse = this.matrixClient.wmsDeliveryCreate(wmsDeliveryCreateReq);
            log.info("通知WMS下发 发货单编号: {} 返回: {}, data内容: {}", matrixWmsDeliveryCreateReq.getOutOrderCode(), matrixResponse, matrixResponse == null ? "" : StringEscapeUtils.unescapeJava(String.valueOf(matrixResponse.getData())));
//            if (matrixResponse != null && StringUtils.isNotBlank(matrixResponse.getMessageId()) && Objects.equals(ApiCommonConst.MATRIX_RESPONSE_SUCCESS, matrixResponse.getResponse())) {
//                HashMap<String, String> data = new ObjectMapper().readValue(String.valueOf(matrixResponse.getData()), new TypeReference<HashMap<String, String>>() {
//                });
//                return data.get("wms_order_code");
//            }
//            throw new BizServiceException(MessageConst.WMS_DELIVERY_PARAMS_ERROR_CODE, String.format(MessageConst.WMS_DELIVERY_PARAMS_ERROR_CONTENT, "接口返回失败"));
            return matrixResponse;
        } catch (JsonProcessingException e) {
            log.warn("通知WMS下发失败 发货单编号: {} 原因: 参数转换失败", matrixWmsDeliveryCreateReq.getOutOrderCode());
            throw new BizServiceException(MessageConst.WMS_DELIVERY_PARAMS_ERROR_CODE, String.format(MessageConst.WMS_DELIVERY_PARAMS_ERROR_CONTENT, "参数转换失败"));
        }
    }

    /**
     * wms退换货单下发
     *
     * @param returnOrderWmsSendReq 请求数据
     */
    public MatrixResponse wmsSyncReturnIssueOrder(ReturnOrderWmsSendReq returnOrderWmsSendReq) {
        ObjectMapper mapper = new ObjectMapper();
        String items = "";
        try {
            WmsSendItemsReq wmsSendItemsReq = returnOrderWmsSendReq.getItems();
            int linuNum = 1;
            for (WmsSendItemReq wmsSendItemReq : wmsSendItemsReq.getItem()) {
                wmsSendItemReq.setItem_line_num(String.valueOf(linuNum));
                linuNum++;
            }
            items = mapper.writeValueAsString(wmsSendItemsReq);
        } catch (IOException e) {
            log.warn("退货单商品信息不正确无法转换成JSON字符串。", e);
        }
        ReturnOrderWmsSendParameter sendParameter = ReturnOrderWmsSendParameter.builder().build();
        BeanUtils.copyProperties(returnOrderWmsSendReq, sendParameter);
        sendParameter.setItems(items);
        return matrixClient.doActionWmsReturnOrderSend(sendParameter);
    }

    /**
     * wms入库单新建
     *
     * @param req 入库单新建请求实体
     * @return 矩阵标准返回体
     */
    public String wmsNoticeGodownCreate(WmsGodownCreateReq req) {
        try {
            // 获取授权信息
            RedundantShopexAuthorization authorization = this.applicationRedundantService.selectShopexAuthorizationToken();
            if (authorization == null) {
                throw new BizDataNotFoundException(MessageConst.NOT_AUTHORIZATION_INFO_CODE, MessageConst.NOT_AUTHORIZATION_INFO_CONTENT);
            }
            WmsNoticeGodownCreateReq wmsNoticeGodownCreateReq = new WmsNoticeGodownCreateReq();
            BeanUtils.copyProperties(req, wmsNoticeGodownCreateReq);
            RelatedOrder relatedOrder = new RelatedOrder();
            BeanUtils.copyProperties(req.getRelatedOrder(), relatedOrder);
            wmsNoticeGodownCreateReq.setRelatedOrder(relatedOrder);
            List<WmsNoticeGodownSkuReq> items = BeanCopyUtils.copyListProperties(req.getItems(), WmsNoticeGodownSkuReq::new);
            // 拼接行号
            for (int i = 0; i < items.size(); i++) {
                items.get(i).setItemLineNum(i+1);
            }
            wmsNoticeGodownCreateReq.setItems(items);
            wmsNoticeGodownCreateReq.setFromNodeId(authorization.getNode());
            wmsNoticeGodownCreateReq.setCertificate(authorization.getCertificate());
            wmsNoticeGodownCreateReq.setToken(authorization.getToken());
            //判断如果req.getOwnerCode为空，则设置为默认值
            if(StringUtils.isBlank(wmsNoticeGodownCreateReq.getOwnerCode())){
                wmsNoticeGodownCreateReq.setOwnerCode(StringUtils.EMPTY);
            }
            log.info("通知wms入库单新建 入库单号: {} 参数: {}", req.getOutOrderCode(), wmsNoticeGodownCreateReq);
            MatrixResponse matrixResponse = this.matrixClient.wmsNoticeGodownCreate(wmsNoticeGodownCreateReq);
            log.info("通知wms入库单新建 入库单号: {} 返回: {}, data内容: {}", req.getOutOrderCode(), matrixResponse, matrixResponse == null ? "" : StringEscapeUtils.unescapeJava(String.valueOf(matrixResponse.getData())));
            if (matrixResponse != null && Objects.equals(ApiCommonConst.MATRIX_RESPONSE_SUCCESS, matrixResponse.getResponse())) {
                return matrixResponse.getResponse();
            }
            throw new BizServiceException(MessageConst.WMS_DELIVERY_PARAMS_ERROR_CODE, String.format(MessageConst.WMS_DELIVERY_PARAMS_ERROR_CONTENT, "接口返回失败"));
        } catch (JsonProcessingException e) {
            log.warn("通知wms入库单新建失败 入库单号: {} 原因: 参数转换失败", req.getOutOrderCode());
            throw new BizServiceException(MessageConst.WMS_DELIVERY_PARAMS_ERROR_CODE, String.format(MessageConst.WMS_DELIVERY_PARAMS_ERROR_CONTENT, "参数转换失败"));
        }
    }

    /**
     * 淘宝退货入仓AG接口
     *
     * @param taobaoAgReturnOrderWmsSendReq 矩阵数据
     * @return 矩阵标准返回体
     */
    public MatrixResponse taobaoAgReturnOrderWmsSend(TaobaoAgReturnOrderWmsSendReq taobaoAgReturnOrderWmsSendReq) {
        TaobaoAgReturnOrderWmsSendParameter build = TaobaoAgReturnOrderWmsSendParameter.builder().build();
        BeanUtils.copyProperties(taobaoAgReturnOrderWmsSendReq, build);
        build.setWarehouseStatus(WmsParamConst.AG_TAOBAO_RETURN_SEND);
        return matrixClient.taobaoAgReturnOrderWmsSend(build);
    }

    /**
     * 京东退货入仓AG接口
     *
     * @param jdAgReturnOrderWmsSendReq 请求数据
     * @return 矩阵标准返回体
     */
    public MatrixResponse jdAgReturnOrderWmsSend(JdAgReturnOrderWmsSendReq jdAgReturnOrderWmsSendReq) {
        JdAgReturnOrderWmsSendParameter param = new JdAgReturnOrderWmsSendParameter();
        BeanUtils.copyProperties(jdAgReturnOrderWmsSendReq, param);
        return matrixClient.jdAgReturnOrderWmsSend(param);
    }

    /**
     * 抖音退货入仓AG接口
     *
     * @param dyAgReturnOrderWmsSendReq 请求数据
     * @return 矩阵标准返回体
     */
    public MatrixResponse dyAgReturnOrderWmsSend(DyAgReturnOrderWmsSendReq dyAgReturnOrderWmsSendReq) {
        DyAgReturnOrderWmsSendParameter param = new DyAgReturnOrderWmsSendParameter();
        BeanUtils.copyProperties(dyAgReturnOrderWmsSendReq, param);
        return matrixClient.dyAgReturnOrderWmsSend(param);
    }

    /**
     * 拼多多退货入仓AG接口
     *
     * @param pddAgReturnOrderWmsSendReq 请求数据
     * @return 矩阵标准返回体
     */
    public MatrixResponse pddAgReturnOrderWmsSend(PddAgReturnOrderWmsSendReq pddAgReturnOrderWmsSendReq) {
        PddAgReturnOrderWmsSendParameter param = new PddAgReturnOrderWmsSendParameter();
        BeanUtils.copyProperties(pddAgReturnOrderWmsSendReq, param);
        return matrixClient.pddAgReturnOrderWmsSend(param);
    }

    /**
     * 小红书售后确认收货接口
     *
     * @param xhsReturnOrderWmsSendReq 矩阵数据
     * @return 矩阵标准返回体
     */
    public MatrixResponse xhsReturnOrderWmsSend(XhsReturnOrderWmsSendReq xhsReturnOrderWmsSendReq) {
        XhsReturnOrderWmsSendParameter build = XhsReturnOrderWmsSendParameter.builder().build();
        BeanUtils.copyProperties(xhsReturnOrderWmsSendReq, build);
        return matrixClient.xhsReturnOrderWmsSend(build);
    }

    /**
     * EZT退货入仓AG接口
     *
     * @param ezrAgReturnOrderWmsSendParameter 请求数据
     * @return 矩阵标准返回体
     */
    public MatrixResponse ezrAgReturnOrderWmsSend(EzrAgReturnOrderWmsSendParameter ezrAgReturnOrderWmsSendParameter) {
        return matrixClient.ezrAgReturnOrderWmsSend(ezrAgReturnOrderWmsSendParameter);
    }

    /**
     * 售前AG接口
     *
     * @param agPreSaleWmsSendReq 矩阵数据
     * @return 请求结果 异步调用
     */
    public MatrixResponse agPreSaleWmsSend(AgPreSaleWmsSendReq agPreSaleWmsSendReq) {
        AgPreSaleWmsSendParameter build = AgPreSaleWmsSendParameter.builder().build();
        BeanUtils.copyProperties(agPreSaleWmsSendReq, build);
        return matrixClient.agPreSaleWmsSend(build);

    }

    /**
     * 平台换货处理
     *
     * @param req 换货请求参数
     * @return 矩阵标准返回体
     */
    public MatrixResponse doStoreExchangeConsigngoods(StoreExchangeConsigngoodsReq req) {
        StoreExchangeConsigngoodsParameter build = StoreExchangeConsigngoodsParameter.builder().build();
        BeanUtils.copyProperties(req, build);
        return matrixClient.doStoreExchangeConsigngoods(build);
    }

    /**
     * 卖家确认收货接口
     *
     * @param req 确认收货请求参数
     * @return 矩阵标准返回体
     */
    public MatrixResponse doStoreExchangeReturngoodsAgree(StoreExchangeReturngoodsAgreeReq req) {
        //neb 卖家确认收货接口
        StoreExchangeReturngoodsAgreeParameter build = StoreExchangeReturngoodsAgreeParameter.builder().build();
        BeanUtils.copyProperties(req, build);
        return matrixClient.doStoreExchangeReturngoodsAgree(build);
    }

    /**
     * wms出库单新建
     *
     * @param req 出库单新建请求实体
     * @return 矩阵标准返回体
     */
    public Boolean wmsNoticeOutboundCreate(WmsNoticeOutboundCreateReq req) {
        try {
            // 获取授权信息
            RedundantShopexAuthorization authorization = this.applicationRedundantService.selectShopexAuthorizationToken();
            if (authorization == null) {
                throw new BizDataNotFoundException(MessageConst.NOT_AUTHORIZATION_INFO_CODE, MessageConst.NOT_AUTHORIZATION_INFO_CONTENT);
            }
            // 操作时间
            LocalDateTime now = LocalDateTime.now();
            String operateTime = formatDate(now, FMT_sdf14_L);
            Long operateTimeUxt = formatUnixTime(now);
            // 授权信息
            req.setFromNodeId(authorization.getNode());
            req.setCertificate(authorization.getCertificate());
            req.setToken(authorization.getToken());
            // 创建时间
            req.setDate(operateTime);
            req.setTimestamp(operateTimeUxt);
            req.setCreated(operateTime);

            //判断如果req.getOwnerCode为空，则设置为默认值
            if(StringUtils.isBlank(req.getOwnerCode())){
                req.setOwnerCode(StringUtils.EMPTY);
            }
            log.info("通知wms出库单新建 出库单号: {} 参数: {}", req.getOutOrderCode(), req);
            MatrixResponse matrixResponse = this.matrixClient.wmsNoticeOutboundCreate(req);
            log.info("通知wms出库单新建 出库单号: {} 返回: {}, data内容: {}", req.getOutOrderCode(), matrixResponse, matrixResponse == null ? "" : StringEscapeUtils.unescapeJava(String.valueOf(matrixResponse.getData())));
            if (matrixResponse != null && Objects.equals(ApiCommonConst.MATRIX_RESPONSE_SUCCESS, matrixResponse.getResponse())) {
                return true;
            }
            throw new BizServiceException(MessageConst.WMS_DELIVERY_PARAMS_ERROR_CODE, String.format(MessageConst.WMS_DELIVERY_PARAMS_ERROR_CONTENT, "接口返回失败"));
        } catch (JsonProcessingException e) {
            log.warn("通知wms出库单新建失败 出库单号: {} 原因: 参数转换失败", req.getOutOrderCode());
            throw new BizServiceException(MessageConst.WMS_DELIVERY_PARAMS_ERROR_CODE, String.format(MessageConst.WMS_DELIVERY_PARAMS_ERROR_CONTENT, "参数转换失败"));
        }
    }

    public Boolean inventoryDetainWms(InventoryDetainWmsReq req) {
        try {
            // 获取授权信息
            RedundantShopexAuthorization authorization = this.applicationRedundantService.selectShopexAuthorizationToken();
            if (authorization == null) {
                throw new BizDataNotFoundException(MessageConst.NOT_AUTHORIZATION_INFO_CODE, MessageConst.NOT_AUTHORIZATION_INFO_CONTENT);
            }
            // 操作时间
            LocalDateTime now = LocalDateTime.now();
            String operateTime = formatDate(now, FMT_sdf14_L);
            Long operateTimeUxt = formatUnixTime(now);
            // 授权信息
            req.setFromNodeId(authorization.getNode());
            req.setCertiId(authorization.getCertificate());
            req.setToken(authorization.getToken());

            req.setAdjust_time(operateTime);
            req.setTimestamp(operateTimeUxt);

            log.info("库存扣留单下发 参数: {}", req);
            MatrixResponse matrixResponse = this.matrixClient.inventoryDetainWms(req);
            log.info("库存扣留单下发 返回: {}, data内容: {}", matrixResponse, matrixResponse == null ? "" : StringEscapeUtils.unescapeJava(String.valueOf(matrixResponse.getData())));
            if (matrixResponse != null && Objects.equals(ApiCommonConst.MATRIX_RESPONSE_SUCCESS, matrixResponse.getResponse())) {
                return true;
            }
            throw new BizServiceException(MessageConst.WMS_DELIVERY_PARAMS_ERROR_CODE, String.format(MessageConst.WMS_DELIVERY_PARAMS_ERROR_CONTENT, "接口返回失败"));
        } catch (JsonProcessingException e) {
            log.warn("库存扣留单下发失败 单号: {} 原因: 参数转换失败", req.getAdjust_order_code());
            throw new BizServiceException(MessageConst.WMS_DELIVERY_PARAMS_ERROR_CODE, String.format(MessageConst.WMS_DELIVERY_PARAMS_ERROR_CONTENT, "参数转换失败"));
        }
    }

    public MatrixResponse wxStoreAgReturnOrderWmsSend(WxStoreAgDto wxStoreAgDto) {
        WxStoreAgParam matrixParam = new WxStoreAgParam();
        BeanUtils.copyProperties(wxStoreAgDto, matrixParam);
        RedundantShopexAuthorization authorization = this.applicationRedundantService.selectShopexAuthorizationToken();
        //系统参数
        matrixParam.setFromNodeId(authorization.getNode());
        matrixParam.setCertiId(authorization.getCertificate());
        matrixParam.setToken(authorization.getToken());
        OnlineStoreDto onlineStoreDto = baseRedundantService.selectOnlineStore(wxStoreAgDto.getStoreCode());
        matrixParam.setToNodeId(onlineStoreDto.getSystemNode());
        return  matrixClient.wxStoreAg(matrixParam);
    }

    public MatrixResponse agKsPreSaleWmsSend(KuaiShouPreSaleAgDto kuaiShouPreSaleAgDto) {
        KuaiShouAgPreSaleParam matrixParam = new KuaiShouAgPreSaleParam();
        BeanUtils.copyProperties(kuaiShouPreSaleAgDto, matrixParam);
        RedundantShopexAuthorization authorization = this.applicationRedundantService.selectShopexAuthorizationToken();
        //系统参数
        matrixParam.setFromNodeId(authorization.getNode());
        matrixParam.setCertiId(authorization.getCertificate());
        matrixParam.setToken(authorization.getToken());
        OnlineStoreDto onlineStoreDto = baseRedundantService.selectOnlineStore(kuaiShouPreSaleAgDto.getStoreCode());
        matrixParam.setToNodeId(onlineStoreDto.getSystemNode());
        return  matrixClient.kuaiShouPreSaleAg(matrixParam);
    }

    public MatrixResponse ksAgReturnOrderWmsSend(KuaiShouReturnOrderAgDto kuaiShouReturnOrderAgDto) {
        KuaiShouReturnOrderParam matrixParam = new KuaiShouReturnOrderParam();
        BeanUtils.copyProperties(kuaiShouReturnOrderAgDto, matrixParam);
        RedundantShopexAuthorization authorization = this.applicationRedundantService.selectShopexAuthorizationToken();
        //系统参数
        matrixParam.setFromNodeId(authorization.getNode());
        matrixParam.setCertiId(authorization.getCertificate());
        matrixParam.setToken(authorization.getToken());
        OnlineStoreDto onlineStoreDto = baseRedundantService.selectOnlineStore(kuaiShouReturnOrderAgDto.getStoreCode());
        matrixParam.setToNodeId(onlineStoreDto.getSystemNode());
        return matrixClient.kuaiShouReturnOrder(matrixParam);
    }

    @SneakyThrows(JsonProcessingException.class)
    public StoreWaybillDto getStoreWaybill(StoreWaybillParam req) {
        MatrixResponse matrixResponse = matrixClient.getShopeeStoreWaybill(req);
        if (StringUtils.isNotBlank(matrixResponse.getMessageId()) && Objects.equals(ApiCommonConst.MATRIX_RESPONSE_SUCCESS, matrixResponse.getResponse())) {
            ObjectMapper mapper = new ObjectMapper();
            mapper.configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true);
            mapper.setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);
            mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            StoreWaybillDto storeWaybillDto = new StoreWaybillDto();
            Map<String, Object> result = mapper.readValue(String.valueOf(matrixResponse.getData()), new TypeReference<Map<String, Object>>() {
            });
            storeWaybillDto.setTrackingNumber(MapUtils.getString(result, "tracking_number", StringUtils.EMPTY));
            storeWaybillDto.setFirstMileTrackingNumber(MapUtils.getString(result, "first_mile_tracking_number", StringUtils.EMPTY));
            log.debug("获取虾皮运单号返回结果: {}", storeWaybillDto);
            return storeWaybillDto;
        }
        return null;
    }

    @SneakyThrows(JsonProcessingException.class)
    public String getLabelUrl(LabelUrlParam req) {
        MatrixResponse matrixResponse = matrixClient.getShopeeLabelUrl(req);
        if (StringUtils.isNotBlank(matrixResponse.getMessageId()) && Objects.equals(ApiCommonConst.MATRIX_RESPONSE_SUCCESS, matrixResponse.getResponse())) {
            ObjectMapper mapper = new ObjectMapper();
            mapper.configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true);
            mapper.setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);
            mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            String result = mapper.writeValueAsString(matrixResponse.getData());
            log.debug("获取虾皮电子面单返回结果: {}", result);
            return result;
        }
        return null;
    }

    @SneakyThrows(JsonProcessingException.class)
    public List<LabelTypeResult> getLabelType(LabelTypeParam req) {
        MatrixResponse matrixResponse = matrixClient.getShopeeLabelType(req);
        if (StringUtils.isNotBlank(matrixResponse.getMessageId()) && Objects.equals(ApiCommonConst.MATRIX_RESPONSE_SUCCESS, matrixResponse.getResponse())) {
            ObjectMapper mapper = new ObjectMapper();
            mapper.configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true);
            mapper.setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);
            mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            Map<String, Object> result = mapper.readValue(String.valueOf(matrixResponse.getData()), new TypeReference<Map<String, Object>>() {
            });
            log.debug("获取虾皮电子面单返回结果: {}", result);
            String resultList = MapUtils.getString(result, "result_list", StringUtils.EMPTY);
            if (StringUtils.isNotBlank(resultList)) {
                List<LabelTypeResponse> labelTypeResponses = mapper.readValue(resultList, new TypeReference<List<LabelTypeResponse>>() {
                });
                if (CollUtil.isNotEmpty(labelTypeResponses)) {
                    // 批量转换
                    return labelTypeResponses.stream()
                            .map(response -> {
                                        String suggestShippingDocumentType;
                                        List<String> selectableShippingDocumentType = response.getSelectableShippingDocumentType();
                                        if (CollUtil.isEmpty(selectableShippingDocumentType)) {
                                            suggestShippingDocumentType = response.getSuggestShippingDocumentType();
                                        } else {
                                            suggestShippingDocumentType = selectableShippingDocumentType.get(0);
                                        }
                                        LabelTypeResult labelTypeResult = new LabelTypeResult();
                                        labelTypeResult.setOrderSn(response.getOrderSn());
                                        labelTypeResult.setPackageNumber(response.getPackageNumber());
                                        labelTypeResult.setSuggestShippingDocumentType(suggestShippingDocumentType);
                                        return labelTypeResult;
                                    }
                            ).collect(Collectors.toList());
                }
            }
            return Lists.newArrayList();
        }
        return Lists.newArrayList();
    }
}
